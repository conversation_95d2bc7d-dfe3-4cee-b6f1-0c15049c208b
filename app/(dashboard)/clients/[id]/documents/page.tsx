"use client";

import { useClientDetails } from '@/components/clients/ClientDetailsContext';
import { useAction, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useState } from "react";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { FileIcon, ExternalLinkIcon, ImageIcon, FileTextIcon, DownloadIcon, XIcon, SendIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DocumentCard } from './DocumentCard';
import { Doc, Id } from '@/convex/_generated/dataModel';
import type { FunctionReturnType } from 'convex/server';

export default function ClientDocumentsPage() {
  const pageData = useClientDetails();
  const documents = useQuery(api.files.clientQueries.getDocumentsForClient, {
    clientId: pageData.client._id
  });
  const syncFromBox = useAction(api.actions.box.syncFromBox);
  const [isSyncing, setIsSyncing] = useState(false);
  
  // Define the document type to match what getDocumentsForClient returns
  type DocumentType = NonNullable<FunctionReturnType<typeof api.files.clientQueries.getDocumentsForClient>>[number];
  
  const [previewDocument, setPreviewDocument] = useState<DocumentType | null>(null);
  const [signRequestDocument, setSignRequestDocument] = useState<DocumentType | null>(null);
  const [signerEmail, setSignerEmail] = useState('');
  const [isSending, setIsSending] = useState(false);
  const sendForSignature = useAction(api.box.signatures.sendForSignature);

  const handleSendForSignature = async () => {
    if (!signRequestDocument || !signerEmail) return;

    // Check if there's a documentId (only formal documents can be signed)
    if (!signRequestDocument.documentId) {
      console.error("This file is not a formal document and cannot be sent for signature");
      // Could add user notification here
      return;
    }

    setIsSending(true);
    try {
      const prepareUrl = await sendForSignature({
        documentId: signRequestDocument.documentId as Id<"documents">,
        signerEmail,
      });

      if (prepareUrl) {
        window.open(prepareUrl, '_blank');
      }

      setSignRequestDocument(null);
      setSignerEmail('');
    } catch (error) {
      console.error("Failed to send for signature:", error);
    } finally {
      setIsSending(false);
    }
  };

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      // @ts-ignore
      await syncFromBox({ clientId: pageData.client._id });
      // Show success message
    } catch (error) {
      console.error(error);
      // Show error message
    } finally {
      setIsSyncing(false);
    }
  };

  // Custom download handler to ensure proper filename
  const handleDownload = async (url: string, fileName: string) => {
    try {
      console.log('Downloading:', fileName, 'from:', url);
      
      // Fetch the file
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Get the blob
      const blob = await response.blob();
      
      // Create a temporary URL for the blob
      const blobUrl = window.URL.createObjectURL(blob);
      
      // Create a temporary anchor element and trigger download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
      
      console.log('Download initiated for:', fileName);
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback to opening in new tab
      window.open(url, '_blank');
    }
  };

  // Helper function to determine file type from filename
  const getFileType = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
      return 'image';
    }
    if (['pdf'].includes(extension || '')) {
      return 'pdf';
    }
    if (['mp4', 'mov', 'avi', 'webm'].includes(extension || '')) {
      return 'video';
    }
    if (['txt', 'md', 'json', 'xml', 'csv'].includes(extension || '')) {
      return 'text';
    }
    return 'other';
  };

  // Helper function to render file preview with thumbnails
  const renderFilePreview = (doc: {fileName: string, url: string | null}) => {
    const fileType = getFileType(doc.fileName);
    
    if (!doc.url) {
      return (
        <div className="w-full aspect-[3/2] bg-gray-100 rounded-md flex items-center justify-center">
          <div className="text-center">
            <FileIcon className="h-6 w-6 text-gray-400 mx-auto mb-1" />
            <span className="text-xs text-gray-500">No preview available</span>
          </div>
        </div>
      );
    }

    switch (fileType) {
      case 'image':
        return (
          <div className="w-full aspect-[3/2] bg-gray-100 rounded-md overflow-hidden relative group">
            <img 
              src={doc.url} 
              alt={doc.fileName}
              className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
              onError={(e) => {
                console.error('Image failed to load:', doc.url);
                const target = e.currentTarget;
                target.style.display = 'none';
                const fallback = document.createElement('div');
                fallback.className = 'w-full h-full flex items-center justify-center';
                fallback.innerHTML = `
                  <div class="text-center">
                    <svg class="h-6 w-6 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span class="text-xs text-gray-500">Image</span>
                  </div>
                `;
                target.parentElement?.appendChild(fallback);
              }}
            />
            {/* Overlay for better visual feedback */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200" />
          </div>
        );
      
      case 'pdf':
        return (
          <div className="w-full aspect-[3/2] bg-gradient-to-br from-red-50 to-red-100 rounded-md relative overflow-hidden group">
            {/* PDF Thumbnail filling the entire space */}
            <div className="absolute inset-0">
              <iframe
                src={`${doc.url}#view=FitH&toolbar=0&navpanes=0&scrollbar=0&zoom=120`}
                className="w-full h-full border-0 pointer-events-none"
                title={`${doc.fileName} thumbnail`}
                style={{ transform: 'scale(1.2)', transformOrigin: 'top left' }}
                onError={() => {
                  console.log('PDF thumbnail failed, showing icon');
                }}
              />
            </div>
            {/* Overlay with icon on hover */}
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-red-50/90 to-red-100/90 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="text-center">
                <FileTextIcon className="h-8 w-8 text-red-500 mx-auto mb-1" />
                <span className="text-sm text-red-700 font-medium">PDF</span>
              </div>
            </div>
          </div>
        );
      
      case 'video':
        return (
          <div className="w-full aspect-[3/2] bg-gradient-to-br from-purple-50 to-purple-100 rounded-md relative overflow-hidden group">
            {/* Video thumbnail with proper aspect ratio */}
            <video 
              className="w-full h-full object-cover"
              preload="metadata"
              muted
              playsInline
              onLoadedMetadata={(e) => {
                // Set video to first frame for thumbnail
                e.currentTarget.currentTime = 0.1;
              }}
              onError={(e) => {
                console.error('Video thumbnail failed:', doc.url);
                const target = e.currentTarget;
                target.style.display = 'none';
              }}
            >
              <source src={doc.url} />
            </video>
            {/* Overlay with play icon */}
            <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/40 transition-colors duration-200">
              <div className="bg-white/90 rounded-full p-2 shadow-lg">
                <svg className="h-6 w-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
          </div>
        );
      
      case 'text':
        return (
          <div className="w-full aspect-[3/2] bg-gradient-to-br from-blue-50 to-blue-100 rounded-md relative overflow-hidden group">
            {/* Text file preview with proper scaling */}
            <div className="absolute inset-2">
              <iframe
                src={doc.url}
                className="w-full h-full border-0 pointer-events-none bg-white rounded shadow-sm"
                title={`${doc.fileName} preview`}
                style={{ fontSize: '8px', zoom: '0.6' }}
                onError={() => {
                  console.log('Text preview failed, showing icon');
                }}
              />
            </div>
            {/* Overlay on hover */}
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50/90 to-blue-100/90 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="text-center">
                <FileTextIcon className="h-8 w-8 text-blue-500 mx-auto mb-1" />
                <span className="text-sm text-blue-700 font-medium">Text</span>
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="w-full aspect-[3/2] bg-gradient-to-br from-gray-50 to-gray-100 rounded-md flex items-center justify-center group hover:from-gray-100 hover:to-gray-200 transition-colors duration-200">
            <div className="text-center">
              <FileIcon className="h-8 w-8 text-gray-500 mx-auto mb-1" />
              <span className="text-sm text-gray-600 font-medium">
                {doc.fileName.split('.').pop()?.toUpperCase() || 'FILE'}
              </span>
            </div>
          </div>
        );
    }
  };

  // Render full file preview in modal
  const renderFullPreview = (doc: {fileName: string, url: string | null}) => {
    const fileType = getFileType(doc.fileName);
    
    if (!doc.url) {
      return (
        <div className="flex items-center justify-center h-96 text-gray-500">
          <div className="text-center">
            <FileIcon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <p>No preview available</p>
          </div>
        </div>
      );
    }

    switch (fileType) {
      case 'image':
        return (
          <div className="max-h-[80vh] overflow-auto">
            <img 
              src={doc.url} 
              alt={doc.fileName}
              className="w-full h-auto rounded-lg"
              onError={(e) => {
                console.error('Image failed to load:', doc.url);
              }}
            />
          </div>
        );
      
      case 'pdf':
        return (
          <div className="h-[80vh] w-full">
            <iframe
              src={doc.url}
              className="w-full h-full rounded-lg border"
              title={doc.fileName}
            />
          </div>
        );
      
      case 'video':
        return (
          <div className="max-h-[80vh]">
            <video 
              controls 
              className="w-full h-auto rounded-lg"
              preload="metadata"
            >
              <source src={doc.url} />
              Your browser does not support the video tag.
            </video>
          </div>
        );
      
      case 'text':
        return (
          <div className="h-96 overflow-auto">
            <iframe
              src={doc.url}
              className="w-full h-full rounded-lg border bg-white"
              title={doc.fileName}
            />
          </div>
        );
      
      default:
        return (
          <div className="flex items-center justify-center h-96 text-gray-500">
            <div className="text-center">
              <FileIcon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <p className="mb-4">Preview not available for this file type</p>
              <Button 
                onClick={() => doc.url && handleDownload(doc.url, doc.fileName)}
                className="flex items-center gap-2"
              >
                <DownloadIcon className="h-4 w-4" />
                Download File
              </Button>
            </div>
          </div>
        );
    }
  };

  // Debug: Log document URLs
  if (documents) {
    console.log('Documents with URLs:', documents.map((doc: DocumentType) => ({ 
      fileName: doc.fileName, 
      url: doc.url,
      hasUrl: !!doc.url 
    })));
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Client Documents</h1>
        <Button onClick={handleSync} disabled={isSyncing}>
          {isSyncing ? "Syncing..." : "Sync from Box"}
        </Button>
      </div>
      
      <div className="mt-6">
        {documents === undefined ? (
          <p>Loading documents...</p>
        ) : documents.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <FileIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No documents found for this client.</p>
              <p className="text-sm text-gray-400 mt-2">
                Use the "Sync from Box" button to import documents.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {documents.map((doc: DocumentType) => (
              <DocumentCard
                key={doc._id}
                doc={doc}
                setPreviewDocument={(docParam) => setPreviewDocument(doc)}
                setSignRequestDocument={(docParam) => setSignRequestDocument(doc)}
                handleDownload={handleDownload}
              />
            ))}
          </div>
        )}
      </div>

      {/* Preview Modal */}
      <Dialog open={!!previewDocument} onOpenChange={(open) => !open && setPreviewDocument(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="truncate mr-4">
                {previewDocument?.fileName}
              </DialogTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => previewDocument && previewDocument.url && handleDownload(previewDocument.url, previewDocument.fileName)}
                  className="flex items-center gap-2"
                >
                  <DownloadIcon className="h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>
          </DialogHeader>
          {previewDocument && renderFullPreview({ ...previewDocument, fileName: previewDocument.fileName })}
        </DialogContent>
      </Dialog>

      {/* Send for Signature Modal */}
      <Dialog open={!!signRequestDocument} onOpenChange={(open) => !open && setSignRequestDocument(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Send for Signature</DialogTitle>
          </DialogHeader>
          <div>
            <p>Document: <strong>{signRequestDocument?.fileName}</strong></p>
            <div className="mt-4">
              <Label htmlFor="signer-email">Signer's Email</Label>
              <Input
                id="signer-email"
                type="email"
                value={signerEmail}
                onChange={(e) => setSignerEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="mt-6 flex justify-end">
              <Button
                onClick={handleSendForSignature}
                disabled={isSending || !signerEmail}
              >
                {isSending ? "Sending..." : "Send"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

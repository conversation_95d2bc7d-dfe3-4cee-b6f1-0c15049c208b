import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery } from '../functions';
import { v } from 'convex/values';
import { Doc, Id } from '../_generated/dataModel';
import {
  getAll,
  getOneFrom,
  getManyFrom,
  getManyVia
} from 'convex-helpers/server/relationships';
import { asyncMap } from 'convex-helpers';
import {
  RelationshipTypeFilterSchema,
  RelationshipFilterSchema,
  EntityTypeSchema,
  EntityIdSchema,
  RelationshipTypeSchema,
  RelationshipSchema,
  RelationshipWithEntitiesSchema,
  PaginatedRelationshipResponseSchema,
  NetworkGraphSchema,
  isValidCombination,
  validateCustomFields,
  RelationshipType,
  Relationship,
  RelationshipWithEntities
} from './relationships-schema';

// ========================================
// RELATIONSHIP TYPE QUERIES
// ========================================

/**
 * Get all relationship types with optional filtering
 */
export const getRelationshipTypes = zQuery({
  args: {
    filter: z.optional(RelationshipTypeFilterSchema),
  },
  output: z.array(RelationshipTypeSchema),
  handler: async (ctx, args): Promise<RelationshipType[]> => {
    const filter = args.filter ?? {};

    const relationshipTypes = await ctx.db
      .query('relationship_types')
      .collect();

    // Filter in memory for complex conditions
    let filteredTypes = relationshipTypes;
    
    if (filter.relationship_category || 
        filter.is_client_family !== undefined || 
        filter.is_bidirectional !== undefined || 
        filter.is_principal_type !== undefined || 
        filter.search_text || 
        filter.source_type || 
        filter.target_type) {
      
      filteredTypes = relationshipTypes.filter(type => {
        if (filter.relationship_category && type.relationship_category !== filter.relationship_category) {
          return false;
        }
        if (filter.is_client_family !== undefined && type.is_client_family !== filter.is_client_family) {
          return false;
        }
        if (filter.is_bidirectional !== undefined && type.is_bidirectional !== filter.is_bidirectional) {
          return false;
        }
        if (filter.is_principal_type !== undefined && type.is_principal_type !== filter.is_principal_type) {
          return false;
        }
        if (filter.search_text) {
          const searchLower = filter.search_text.toLowerCase();
          const nameMatch = type.relationship_name.toLowerCase().includes(searchLower);
          const descMatch = type.relationship_description?.toLowerCase().includes(searchLower) ?? false;
          if (!nameMatch && !descMatch) {
            return false;
          }
        }
        
        // Check valid combinations
        if (filter.source_type || filter.target_type) {
          const hasValidCombo = type.valid_combinations.some((combo: { source_type: string; target_type: string }) => {
            const sourceMatch = !filter.source_type || combo.source_type === filter.source_type;
            const targetMatch = !filter.target_type || combo.target_type === filter.target_type;
            return sourceMatch && targetMatch;
          });
          if (!hasValidCombo) {
            return false;
          }
        }
        
        return true;
      });
    }

    // Map to match schema structure - no casting needed
    return filteredTypes.map(rt => ({
      _id: rt._id as Id<"relationship_types">,
      _creationTime: rt._creationTime,
      relationship_name: rt.relationship_name,
      relationship_description: rt.relationship_description,
      relationship_category: rt.relationship_category,
      valid_combinations: rt.valid_combinations,
      is_client_family: rt.is_client_family,
      is_bidirectional: rt.is_bidirectional,
      implies_client_status: rt.implies_client_status,
      is_principal_type: rt.is_principal_type,
      custom_fields_schema: rt.custom_fields_schema,
      updated_at: rt.updated_at
    }));
  },
});

/**
 * Get a specific relationship type by ID
 */
export const getRelationshipType = zQuery({
  args: {
    relationshipTypeId: zid("relationship_types")
  },
  output: z.union([RelationshipTypeSchema, z.null()]),
  handler: async (ctx, args): Promise<RelationshipType | null> => {
    const relationshipType = await ctx.db.get(args.relationshipTypeId);
    
    if (!relationshipType) {
      return null;
    }

    // Map to match schema structure - no casting needed
    return {
      _id: relationshipType._id as Id<"relationship_types">,
      _creationTime: relationshipType._creationTime,
      relationship_name: relationshipType.relationship_name,
      relationship_description: relationshipType.relationship_description,
      relationship_category: relationshipType.relationship_category,
      valid_combinations: relationshipType.valid_combinations,
      is_client_family: relationshipType.is_client_family,
      is_bidirectional: relationshipType.is_bidirectional,
      implies_client_status: relationshipType.implies_client_status,
      is_principal_type: relationshipType.is_principal_type,
      custom_fields_schema: relationshipType.custom_fields_schema,
      updated_at: relationshipType.updated_at
    };
  },
});

/**
 * Get a specific relationship type by name
 */
export const getRelationshipTypeByName = zQuery({
  args: {
    name: z.string(),
  },
  output: z.union([RelationshipTypeSchema, z.null()]),
  handler: async (ctx, args): Promise<RelationshipType | null> => {
    const relationshipType = await ctx.db
      .query('relationship_types')
      .withIndex('by_name', q => q.eq('relationship_name', args.name))
      .unique();
    
    if (!relationshipType) {
      return null;
    }

    // Map to match schema structure - no casting needed
    return {
      _id: relationshipType._id as Id<"relationship_types">,
      _creationTime: relationshipType._creationTime,
      relationship_name: relationshipType.relationship_name,
      relationship_description: relationshipType.relationship_description,
      relationship_category: relationshipType.relationship_category,
      valid_combinations: relationshipType.valid_combinations,
      is_client_family: relationshipType.is_client_family,
      is_bidirectional: relationshipType.is_bidirectional,
      implies_client_status: relationshipType.implies_client_status,
      is_principal_type: relationshipType.is_principal_type,
      custom_fields_schema: relationshipType.custom_fields_schema,
      updated_at: relationshipType.updated_at
    };
  },
});

/**
 * Get available relationship types for a specific entity combination
 */
export const getAvailableRelationshipTypes = zQuery({
  args: {
    sourceType: EntityTypeSchema,
    targetType: EntityTypeSchema,
  },
  output: z.array(RelationshipTypeSchema),
  handler: async (ctx, args): Promise<RelationshipType[]> => {
    const { sourceType, targetType } = args;

    const allTypes = await ctx.db
      .query('relationship_types')
      .collect();

    const availableTypes = allTypes.filter(type =>
      isValidCombination(type as any, sourceType, targetType),
    );

    // Map to match schema structure - no casting needed
    return availableTypes.map(rt => ({
      _id: rt._id as Id<"relationship_types">,
      _creationTime: rt._creationTime,
      relationship_name: rt.relationship_name,
      relationship_description: rt.relationship_description,
      relationship_category: rt.relationship_category,
      valid_combinations: rt.valid_combinations,
      is_client_family: rt.is_client_family,
      is_bidirectional: rt.is_bidirectional,
      implies_client_status: rt.implies_client_status,
      is_principal_type: rt.is_principal_type,
      custom_fields_schema: rt.custom_fields_schema,
      updated_at: rt.updated_at
    }));
  },
});

// ========================================
// RELATIONSHIP QUERIES
// ========================================

/**
 * Get relationships with filtering and pagination
 */
export const getRelationships = zQuery({
  args: {
    filter: z.optional(RelationshipFilterSchema),
    pagination: z.object({
      cursor: z.union([z.string(), z.null()]).optional(),
      numItems: z.number().optional()
    }),
  },
  output: PaginatedRelationshipResponseSchema,
  handler: async (ctx, args) => {
    const filter = args.filter;

    let query = ctx.db.query("relationships");

    // Apply filters where possible
    if (filter?.is_active !== undefined) {
      query = query.filter(q => q.eq(q.field("is_active"), filter.is_active));
    }

    // Execute paginated query
    const paginationOptions = {
      cursor: args.pagination.cursor ?? null,
      numItems: args.pagination.numItems ?? 50
    };
    const result = await query.order("desc").paginate(paginationOptions);

    // Post-process to apply remaining filters and enrich data
    let relationships = result.page;

    // Apply additional filters in memory
    if (filter) {
      relationships = relationships.filter(rel => {
        if (filter.source_type && filter.source_id) {
          if (rel.source_type !== filter.source_type || rel.source_id !== filter.source_id) {
            return false;
          }
        }
        if (filter.target_type && filter.target_id) {
          if (rel.target_type !== filter.target_type || rel.target_id !== filter.target_id) {
            return false;
          }
        }
        if (filter.relationship_type_id && rel.relationship_type_id !== filter.relationship_type_id) {
          return false;
        }
        if (filter.relationship_start_from && (!rel.relationship_start || rel.relationship_start < filter.relationship_start_from)) {
          return false;
        }
        if (filter.relationship_start_to && (!rel.relationship_start || rel.relationship_start > filter.relationship_start_to)) {
          return false;
        }
        if (filter.relationship_end_from && (!rel.relationship_end || rel.relationship_end < filter.relationship_end_from)) {
          return false;
        }
        if (filter.relationship_end_to && (!rel.relationship_end || rel.relationship_end > filter.relationship_end_to)) {
          return false;
        }
        if (filter.min_authority_limit && (!rel.authority_limit || rel.authority_limit < filter.min_authority_limit)) {
          return false;
        }
        if (filter.max_authority_limit && (!rel.authority_limit || rel.authority_limit > filter.max_authority_limit)) {
          return false;
        }
        return true;
      });
    }

    // Batch fetch relationship types
    const relationshipTypeIds = [...new Set(relationships.map(rel => rel.relationship_type_id))];
    const relationshipTypes = await getAll(ctx.db, relationshipTypeIds);
    const typeMap = new Map<string, Doc<"relationship_types"> | null>();
    relationshipTypeIds.forEach((id, index) => {
      typeMap.set(id, relationshipTypes[index]);
    });
    
    // Collect all entity references for batch resolution
    const entityRefs = relationships.flatMap(rel => [
      { type: rel.source_type, id: rel.source_id },
      { type: rel.target_type, id: rel.target_id }
    ]);
    const resolvedEntityMap = await resolveEntitiesBatch(ctx, entityRefs);

    // Use asyncMap for parallel processing of relationships
    const relationshipsWithEntities = await asyncMap(relationships, async (relationship) => {
      const relationshipType = typeMap.get(relationship.relationship_type_id) || null;
      const sourceKey = `${relationship.source_type}:${relationship.source_id}`;
      const targetKey = `${relationship.target_type}:${relationship.target_id}`;

      // Map relationship type to match schema
      const mappedRelationshipType = relationshipType ? {
        _id: relationshipType._id as Id<"relationship_types">,
        _creationTime: relationshipType._creationTime,
        relationship_name: relationshipType.relationship_name,
        relationship_description: relationshipType.relationship_description,
        relationship_category: relationshipType.relationship_category,
        valid_combinations: relationshipType.valid_combinations,
        is_client_family: relationshipType.is_client_family,
        is_bidirectional: relationshipType.is_bidirectional,
        implies_client_status: relationshipType.implies_client_status,
        is_principal_type: relationshipType.is_principal_type,
        custom_fields_schema: relationshipType.custom_fields_schema,
        updated_at: relationshipType.updated_at
      } : null;

      return {
        _id: relationship._id as Id<"relationships">,
        _creationTime: relationship._creationTime,
        source_type: relationship.source_type,
        source_id: relationship.source_id,
        target_type: relationship.target_type,
        target_id: relationship.target_id,
        relationship_type_id: relationship.relationship_type_id,
        relationship_start: relationship.relationship_start,
        relationship_end: relationship.relationship_end,
        is_active: relationship.is_active,
        relationship_notes: relationship.relationship_notes,
        authority_limit: relationship.authority_limit,
        custom_fields: relationship.custom_fields,
        updated_at: relationship.updated_at,
        relationship_type: mappedRelationshipType,
        source_entity: resolvedEntityMap.get(sourceKey) || null,
        target_entity: resolvedEntityMap.get(targetKey) || null
      };
    });

    return {
      ...result,
      page: relationshipsWithEntities,
      continueCursor: result.continueCursor || null
    };
  },
});

/**
 * Get a specific relationship by ID
 */
export const getRelationship = zQuery({
  args: {
    relationshipId: zid("relationships")
  },
  output: z.union([RelationshipWithEntitiesSchema, z.null()]),
  handler: async (ctx, args) => {
    const relationship = await ctx.db.get(args.relationshipId);
    
    if (!relationship) {
      return null;
    }

    // Get relationship type
    const relationshipType = await ctx.db.get(relationship.relationship_type_id);

    // Batch resolve entities
    const entityRefs = [
      { type: relationship.source_type, id: relationship.source_id },
      { type: relationship.target_type, id: relationship.target_id }
    ];
    const resolvedEntityMap = await resolveEntitiesBatch(ctx, entityRefs);
    
    const sourceKey = `${relationship.source_type}:${relationship.source_id}`;
    const targetKey = `${relationship.target_type}:${relationship.target_id}`;

    // Map relationship type to match schema
    const mappedRelationshipType = relationshipType ? {
      _id: relationshipType._id as Id<"relationship_types">,
      _creationTime: relationshipType._creationTime,
      relationship_name: relationshipType.relationship_name,
      relationship_description: relationshipType.relationship_description,
      relationship_category: relationshipType.relationship_category,
      valid_combinations: relationshipType.valid_combinations,
      is_client_family: relationshipType.is_client_family,
      is_bidirectional: relationshipType.is_bidirectional,
      implies_client_status: relationshipType.implies_client_status,
      is_principal_type: relationshipType.is_principal_type,
      custom_fields_schema: relationshipType.custom_fields_schema,
      updated_at: relationshipType.updated_at
    } : null;

    return {
      _id: relationship._id as Id<"relationships">,
      _creationTime: relationship._creationTime,
      source_type: relationship.source_type,
      source_id: relationship.source_id,
      target_type: relationship.target_type,
      target_id: relationship.target_id,
      relationship_type_id: relationship.relationship_type_id,
      relationship_start: relationship.relationship_start,
      relationship_end: relationship.relationship_end,
      is_active: relationship.is_active,
      relationship_notes: relationship.relationship_notes,
      authority_limit: relationship.authority_limit,
      custom_fields: relationship.custom_fields,
      updated_at: relationship.updated_at,
      relationship_type: mappedRelationshipType,
      source_entity: resolvedEntityMap.get(sourceKey) || null,
      target_entity: resolvedEntityMap.get(targetKey) || null
    };
  },
});

/**
 * Get all relationships for a specific entity
 */
export const getEntityRelationships = zQuery({
  args: {
    entityType: EntityTypeSchema,
    entityId: z.string(),
  },
  output: z.array(RelationshipWithEntitiesSchema),
  handler: async (ctx, args) => {
    const { entityType, entityId } = args;

    // Get all relationships for this entity
    const allRelationships = await ctx.db
      .query('relationships')
      .filter(q =>
        q.and(
          q.eq(q.field('is_active'), true),
          q.or(
            q.and(
              q.eq(q.field('source_type'), entityType),
              q.eq(q.field('source_id'), entityId)
            ),
            q.and(
              q.eq(q.field('target_type'), entityType),
              q.eq(q.field('target_id'), entityId)
            )
          )
        )
      )
      .collect();

    // Batch process using helpers
    const relationshipTypeIds = [...new Set(allRelationships.map(rel => rel.relationship_type_id))];
    const relationshipTypes = await getAll(ctx.db, relationshipTypeIds);
    const typeMap = new Map<string, Doc<"relationship_types"> | null>();
    relationshipTypeIds.forEach((id, index) => {
      typeMap.set(id, relationshipTypes[index]);
    });
    
    const entityRefs = allRelationships.flatMap(rel => [
      { type: rel.source_type, id: rel.source_id },
      { type: rel.target_type, id: rel.target_id }
    ]);
    const resolvedEntityMap = await resolveEntitiesBatch(ctx, entityRefs);

    // Use asyncMap for parallel processing
    const relationshipsWithEntities = await asyncMap(allRelationships, async (relationship) => {
      const relationshipType = typeMap.get(relationship.relationship_type_id) || null;
      const sourceKey = `${relationship.source_type}:${relationship.source_id}`;
      const targetKey = `${relationship.target_type}:${relationship.target_id}`;

      // Map relationship type to match schema
      const mappedRelationshipType = relationshipType ? {
        _id: relationshipType._id as Id<"relationship_types">,
        _creationTime: relationshipType._creationTime,
        relationship_name: relationshipType.relationship_name,
        relationship_description: relationshipType.relationship_description,
        relationship_category: relationshipType.relationship_category,
        valid_combinations: relationshipType.valid_combinations,
        is_client_family: relationshipType.is_client_family,
        is_bidirectional: relationshipType.is_bidirectional,
        implies_client_status: relationshipType.implies_client_status,
        is_principal_type: relationshipType.is_principal_type,
        custom_fields_schema: relationshipType.custom_fields_schema,
        updated_at: relationshipType.updated_at
      } : null;

      return {
        _id: relationship._id as Id<"relationships">,
        _creationTime: relationship._creationTime,
        source_type: relationship.source_type,
        source_id: relationship.source_id,
        target_type: relationship.target_type,
        target_id: relationship.target_id,
        relationship_type_id: relationship.relationship_type_id,
        relationship_start: relationship.relationship_start,
        relationship_end: relationship.relationship_end,
        is_active: relationship.is_active,
        relationship_notes: relationship.relationship_notes,
        authority_limit: relationship.authority_limit,
        custom_fields: relationship.custom_fields,
        updated_at: relationship.updated_at,
        relationship_type: mappedRelationshipType,
        source_entity: resolvedEntityMap.get(sourceKey) || null,
        target_entity: resolvedEntityMap.get(targetKey) || null
      };
    });

    return relationshipsWithEntities;
  },
});

// ========================================
// NETWORK ANALYSIS QUERIES
// ========================================

/**
 * Get network graph for entities and their relationships
 */
export const getNetworkGraph = zQuery({
  args: {
    centerEntityType: z.optional(EntityTypeSchema),
    centerEntityId: z.optional(z.string()),
    maxDepth: z.optional(z.number()),
    relationshipCategories: z.optional(z.array(z.string()))
  },
  output: NetworkGraphSchema,
  handler: async (ctx, args) => {
    const maxDepth = args.maxDepth ?? 2;
    const nodes = new Map();
    const edges = new Map();
    const visited = new Set();

    // Helper function to add entity to network
    const addEntityToNetwork = async (entityType: string, entityId: string) => {
      const key = `${entityType}:${entityId}`;
      if (visited.has(key)) return;
      visited.add(key);

      const entity = await resolveEntity(ctx, entityType as any, entityId);
      if (entity) {
        nodes.set(key, {
          id: key,
          type: entityType,
          name: entity.name,
          description: entity.description
        });
      }
    };

    // Start with center entity if provided
    if (args.centerEntityType && args.centerEntityId) {
      await addEntityToNetwork(args.centerEntityType, args.centerEntityId);
      
      // Get relationships for center entity
      const relationships = await ctx.db
        .query("relationships")
        .filter(q => 
          q.and(
            q.or(
              q.and(
                q.eq(q.field("source_type"), args.centerEntityType),
                q.eq(q.field("source_id"), args.centerEntityId)
              ),
              q.and(
                q.eq(q.field("target_type"), args.centerEntityType),
                q.eq(q.field("target_id"), args.centerEntityId)
              )
            ),
            q.eq(q.field("is_active"), true)
          )
        )
        .collect();

      // Batch process relationships and their types
      const relationshipTypeIds = relationships.map(rel => rel.relationship_type_id);
      const relationshipTypes = await getAll(ctx.db, relationshipTypeIds);
      
      for (let i = 0; i < relationships.length; i++) {
        const rel = relationships[i];
        const relationshipType = relationshipTypes[i];
        
        if (!relationshipType) continue;

        // Add related entities
        await addEntityToNetwork(rel.source_type, rel.source_id);
        await addEntityToNetwork(rel.target_type, rel.target_id);

        // Add edge
        const edgeKey = `${rel._id}`;
        edges.set(edgeKey, {
          id: edgeKey,
          source: `${rel.source_type}:${rel.source_id}`,
          target: `${rel.target_type}:${rel.target_id}`,
          relationship_type: relationshipType.relationship_name,
          is_bidirectional: relationshipType.is_bidirectional,
          strength: 1
        });
      }
    } else {
      // Get all active relationships if no center entity specified
      const allRelationships = await ctx.db
        .query("relationships")
        .filter(q => q.eq(q.field("is_active"), true))
        .take(100); // Limit for performance

      for (const rel of allRelationships) {
        const [sourceEntity, targetEntity, relationshipType] = await Promise.all([
          resolveEntity(ctx, rel.source_type, rel.source_id),
          resolveEntity(ctx, rel.target_type, rel.target_id),
          ctx.db.get(rel.relationship_type_id)
        ]);

        if (sourceEntity && targetEntity && relationshipType) {
          // Add entities
          const sourceKey = `${rel.source_type}:${rel.source_id}`;
          const targetKey = `${rel.target_type}:${rel.target_id}`;

          nodes.set(sourceKey, {
            id: sourceKey,
            type: rel.source_type,
            name: sourceEntity.name,
            description: sourceEntity.description
          });

          nodes.set(targetKey, {
            id: targetKey,
            type: rel.target_type,
            name: targetEntity.name,
            description: targetEntity.description
          });

          // Add edge
          const edgeKey = `${rel._id}`;
          edges.set(edgeKey, {
            id: edgeKey,
            source: sourceKey,
            target: targetKey,
            relationship_type: relationshipType.relationship_name,
            is_bidirectional: relationshipType.is_bidirectional,
            strength: 1
          });
        }
      }
    }

    return {
      nodes: Array.from(nodes.values()),
      edges: Array.from(edges.values())
    };
  },
});

// ========================================
// ENTITY SEARCH QUERIES
// ========================================

// Define entity result schema
const EntitySearchResultSchema = z.object({
  _id: z.string(),
  name: z.string(),
  type: EntityTypeSchema,
  description: z.optional(z.string()),
  imageUrl: z.optional(z.string())
});

/**
 * Search entities for relationships
 */
export const searchEntitiesForRelationships = zQuery({
  args: {
    searchTerm: z.string(),
    entityType: EntityTypeSchema,
    limit: z.optional(z.number())
  },
  output: z.array(EntitySearchResultSchema),
  handler: async (ctx, args) => {
    const { searchTerm, entityType, limit = 10 } = args;
    
    if (!searchTerm.trim()) {
      return [];
    }

    let results: any[] = [];

    switch (entityType) {
      case 'person':
        results = await ctx.db
          .query("people")
          .withSearchIndex("search_name", (q) => q.search("name", searchTerm))
          .take(limit);
        break;
      case 'organization':
        results = await ctx.db
          .query("organizations")
          .withSearchIndex("search_name", (q) => q.search("name", searchTerm))
          .take(limit);
        break;
      case 'client':
        results = await ctx.db
          .query("clients")
          .withSearchIndex("search_name", (q) => q.search("client_name", searchTerm))
          .take(limit);
        break;
      default:
        return [];
    }

    return results.map(entity => ({
      _id: entity._id as string,
      name: entity.name || entity.client_name,
      type: entityType,
      description: entity.description || entity.client_description,
      imageUrl: entity.image
    }));
  },
});

// Define person schema for search results
const PersonSearchResultSchema = z.object({
  _id: z.string(),
  name: z.string(),
  description: z.optional(z.string()),
  image: z.optional(z.string())
});

/**
 * Search people for principal relationships
 */
export const searchPeopleForPrincipal = zQuery({
  args: {
    searchTerm: z.string(),
    limit: z.optional(z.number())
  },
  output: z.array(PersonSearchResultSchema),
  handler: async (ctx, args) => {
    const { searchTerm, limit = 10 } = args;
    
    if (!searchTerm.trim()) {
      return [];
    }

    const people = await ctx.db
      .query("people")
      .withSearchIndex("search_name", (q) => q.search("name", searchTerm))
      .take(limit);

    return people.map(person => ({
      _id: person._id as string,
      name: person.name,
      description: person.description,
      image: person.image
    }));
  },
});

/**
 * Get non-principal relationship types
 */
export const getNonPrincipalRelationshipTypes = zQuery({
  args: {},
  output: z.array(RelationshipTypeSchema),
  handler: async (ctx, args) => {
    const relationshipTypes = await ctx.db
      .query("relationship_types")
      .filter(q => q.eq(q.field("is_principal_type"), false))
      .collect();

    // Map to match the expected schema structure
    return relationshipTypes.map(rt => ({
      _id: rt._id as Id<"relationship_types">,
      _creationTime: rt._creationTime,
      relationship_name: rt.relationship_name,
      relationship_description: rt.relationship_description,
      relationship_category: rt.relationship_category,
      valid_combinations: rt.valid_combinations,
      is_client_family: rt.is_client_family,
      is_bidirectional: rt.is_bidirectional,
      implies_client_status: rt.implies_client_status,
      is_principal_type: rt.is_principal_type,
      custom_fields_schema: rt.custom_fields_schema,
      updated_at: rt.updated_at
    }));
  },
});

/**
 * List relationship types for client (compatibility function)
 */
export const listRelationshipTypesForClient = zQuery({
  args: {},
  output: z.array(RelationshipTypeSchema),
  handler: async (ctx, args) => {
    const relationshipTypes = await ctx.db
      .query("relationship_types")
      .collect();
    
    // Map to match schema structure like other functions do
    return relationshipTypes.map(rt => ({
      _id: rt._id as Id<"relationship_types">,
      _creationTime: rt._creationTime,
      relationship_name: rt.relationship_name,
      relationship_description: rt.relationship_description,
      relationship_category: rt.relationship_category,
      valid_combinations: rt.valid_combinations,
      is_client_family: rt.is_client_family,
      is_bidirectional: rt.is_bidirectional,
      implies_client_status: rt.implies_client_status,
      is_principal_type: rt.is_principal_type,
      custom_fields_schema: rt.custom_fields_schema,
      updated_at: rt.updated_at
    }));
  },
});

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Resolve an entity by type and ID to get basic information
 */
async function resolveEntity(ctx: any, entityType: string, entityId: string) {
  let entity: Doc<"people"> | Doc<"organizations"> | Doc<"clients"> | null = null;
  
  switch (entityType) {
    case 'person':
      entity = await ctx.db.get(entityId as Id<"people">);
      break;
    case 'organization':
      entity = await ctx.db.get(entityId as Id<"organizations">);
      break;
    case 'client':
      entity = await ctx.db.get(entityId as Id<"clients">);
      break;
    default:
      return null;
  }

  if (!entity) return null;

  return {
    _id: entity._id,
    name: (entity as any).name || (entity as any).client_name,
    type: entityType,
    description: (entity as any).description || (entity as any).client_description || undefined
  };
}

/**
 * Batch resolve multiple entities using getAll helper
 */
async function resolveEntitiesBatch(ctx: any, entities: Array<{type: string, id: string}>) {
  // Group entities by type for batch operations
  const entitiesByType = entities.reduce((acc, entity) => {
    if (!acc[entity.type]) acc[entity.type] = [];
    acc[entity.type].push(entity.id);
    return acc;
  }, {} as Record<string, string[]>);

  const resolvedEntities = new Map();

  // Batch fetch each entity type
  for (const [entityType, ids] of Object.entries(entitiesByType)) {
    switch (entityType) {
      case 'person':
        const people = await getAll(ctx.db, ids as Id<"people">[]);
        people.forEach((person, index) => {
          if (person) {
            const key = `person:${ids[index]}`;
            resolvedEntities.set(key, {
              _id: person._id,
              name: person.name,
              type: 'person',
              description: person.description
            });
          }
        });
        break;
        
      case 'organization':
        const orgs = await getAll(ctx.db, ids as Id<"organizations">[]);
        orgs.forEach((org, index) => {
          if (org) {
            const key = `organization:${ids[index]}`;
            resolvedEntities.set(key, {
              _id: org._id,
              name: org.name,
              type: 'organization',
              description: org.description
            });
          }
        });
        break;
        
      case 'client':
        const clients = await getAll(ctx.db, ids as Id<"clients">[]);
        clients.forEach((client, index) => {
          if (client) {
            const key = `client:${ids[index]}`;
            resolvedEntities.set(key, {
              _id: client._id,
              name: client.client_name,
              type: 'client',
              description: client.client_description
            });
          }
        });
        break;
        
      default:
        continue;
    }
  }

  return resolvedEntities;
}

/**
 * Validate relationship custom fields against type schema
 */
export const validateRelationshipCustomFields = zQuery({
  args: {
    relationshipTypeId: zid("relationship_types"),
    customFields: z.record(z.unknown())
  },
  output: z.object({
    success: z.boolean(),
    error: z.optional(z.string())
  }),
  handler: async (ctx, args) => {
    const relationshipType = await ctx.db.get(args.relationshipTypeId);
    
    if (!relationshipType) {
      return {
        success: false,
        error: "Relationship type not found"
      };
    }

    if (!relationshipType.custom_fields_schema) {
      return { success: true };
    }

    try {
      const validation = validateCustomFields(args.customFields, relationshipType.custom_fields_schema);
      return {
        success: validation.success,
        error: validation.success ? undefined : validation.error?.message
      };
    } catch (error) {
      return {
        success: false,
        error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  },
});
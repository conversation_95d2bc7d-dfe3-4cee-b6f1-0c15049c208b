// functions.ts
import { zCustomQuery, zCustomMutation, zCustomAction } from 'convex-helpers/server/zod';
import { NoOp, customCtx } from 'convex-helpers/server/customFunctions';
import { query, mutation, action, internalQuery } from './_generated/server';
import { api } from './_generated/api'; // Correct import for api
import { Triggers } from 'convex-helpers/server/triggers';
import { DataModel, Id } from './_generated/dataModel'; // Import Id
import { scheduleFieldGeneration, SHORT_DESCRIPTION_DEBOUNCE_DELAY } from './utils/shortDescriptionUtils'; // Import delay constant

// Create a Triggers instance for the schema
const triggers = new Triggers<DataModel>();

// Register a trigger for the "general_decisions" subtable
triggers.register("general_decisions", async (ctx, change) => {
  // Check if it's an update operation and the description has changed
  if (
    (change.operation === "insert" && change.newDoc?.description !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.description !== change.newDoc?.description &&
     change.newDoc?.description !== undefined)
  ) {
    console.log("General decision description changed, scheduling short description generation");

    // Get the parent decision ID
    const decisionId = change.newDoc.decision_id;
    
    // Revert to simple scheduling as debouncing isn't set up for decisions yet
    console.log(`General decision description changed for decision ${decisionId}, scheduling short description generation`);
    await ctx.scheduler.runAfter(0, api.actions.orchestrators.generateAndStoreField, {
      tableName: "decisions",
      documentId: decisionId.toString(),
      sourceText: change.newDoc.description,
      targetFieldName: "short_description",
      promptSlug: "short-description",
    });
  }
});

// Register a trigger for the "investment_decisions" subtable
triggers.register("investment_decisions", async (ctx, change) => {
  // Check if it's an update operation and the description has changed
  if (
    (change.operation === "insert" && change.newDoc?.description !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.description !== change.newDoc?.description &&
     change.newDoc?.description !== undefined)
  ) {
    console.log("Investment decision description changed, scheduling short description generation");

    // Get the parent decision ID
    const decisionId = change.newDoc.decision_id;
    
    // Revert to simple scheduling as debouncing isn't set up for decisions yet
    console.log(`Investment decision description changed for decision ${decisionId}, scheduling short description generation`);
    await ctx.scheduler.runAfter(0, api.actions.orchestrators.generateAndStoreField, {
      tableName: "decisions",
      documentId: decisionId.toString(),
      sourceText: change.newDoc.description,
      targetFieldName: "short_description",
      promptSlug: "short-description",
    });
  }
});

// Note: We'll need to add a trigger for custom_decisions once it's implemented
// For now, we're commenting this out to avoid TypeScript errors
/*
// Register a trigger for the "custom_decisions" subtable
triggers.register("custom_decisions", async (ctx, change) => {
  // Implementation will depend on the final structure of custom_decisions table
  // This is a placeholder for future implementation
});
*/

// Register a trigger for the "tasks" table
triggers.register("tasks", async (ctx, change) => {
  // Check if it's a new task with a description or if the description has been updated
  if (
    (change.operation === "insert" && change.newDoc?.description !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.description !== change.newDoc?.description &&
     change.newDoc?.description !== undefined)
  ) {
    console.log("Task description changed, scheduling short description generation");

    // Revert to simple scheduling as debouncing isn't set up for tasks yet
    console.log("Task description changed, scheduling short description generation (no debounce)");
    await ctx.scheduler.runAfter(0, api.actions.orchestrators.generateAndStoreField, {
      tableName: "tasks",
      documentId: change.id.toString(),
      sourceText: change.newDoc.description,
      targetFieldName: "short_description",
      promptSlug: "short-description",
    });
  }
});

// Register a trigger for the "projects" table
triggers.register("projects", async (ctx, change) => {
  // Check if it's a new project with a description or if the description has been updated
  if (
    (change.operation === "insert" && change.newDoc?.description !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.description !== change.newDoc?.description &&
     change.newDoc?.description !== undefined)
  ) {
    console.log("Project description changed, scheduling short description generation with debouncing");

    // Use the updated scheduleFieldGeneration helper with debouncing
    await scheduleFieldGeneration(
      ctx,
      "projects",             // Table name
      change.id,              // Document ID
      change.newDoc.description, // Source text
      "short_description",    // Target field
      "short-description",    // Prompt slug (using the same generic one for now)
      "shortDescriptionJobId", // Field to store the job ID for debouncing
      SHORT_DESCRIPTION_DEBOUNCE_DELAY // Use the standard delay
    );
  }
});

// --- NEW TRIGGERS FOR FILES ---

// Register a trigger for the "knowledge_base" table
triggers.register("knowledge_base", async (ctx, change) => {
  // Check if it's a new KB article with content or if the content has been updated
  if (
    (change.operation === "insert" && change.newDoc?.content !== undefined && change.newDoc?.content.trim().length > 0) ||
    (change.operation === "update" &&
     change.oldDoc?.content !== change.newDoc?.content &&
     change.newDoc?.content !== undefined &&
     change.newDoc?.content.trim().length > 0)
  ) {
    const fileId = change.newDoc.fileId;
    const content = change.newDoc.content;

    console.log(`KB content changed for file ${fileId}, scheduling short description generation`);

    // Use the updated scheduleFieldGeneration helper with debouncing for files
    await scheduleFieldGeneration(
      ctx,
      "files",                  // Table name
      fileId,                   // Document ID
      content,                  // Source text
      "short_description",      // Target field
      "docs-short-description", // Prompt slug
      "shortDescriptionJobId",  // Field to store the job ID for debouncing
      SHORT_DESCRIPTION_DEBOUNCE_DELAY // Use the standard delay
    );
  }
});

// Register a trigger for the "meeting_notes" table to handle transcript updates
triggers.register("meeting_notes", async (ctx, change) => {
  console.log(`[Trigger meeting_notes] Fired for operation: ${change.operation}, ID: ${change.id}`);

  // Check if it's a transcript update
  const isTranscriptUpdate = change.operation === "update" &&
                             change.oldDoc?.transcript !== change.newDoc?.transcript &&
                             change.newDoc?.transcript !== undefined;

  console.log(`[Trigger meeting_notes] Is transcript update? ${isTranscriptUpdate}`);
  if (change.operation === "update") {
    console.log(`[Trigger meeting_notes] Old transcript length: ${change.oldDoc?.transcript?.length ?? 'N/A'}`);
    console.log(`[Trigger meeting_notes] New transcript length: ${change.newDoc?.transcript?.length ?? 'N/A'}`);
    console.log(`[Trigger meeting_notes] Transcripts different? ${change.oldDoc?.transcript !== change.newDoc?.transcript}`);
  }

  if (isTranscriptUpdate) {
    const fileId = change.newDoc.fileId; // Keep this declaration
    console.log(`[Trigger meeting_notes] Transcript updated for fileId ${fileId}. Attempting to schedule AI notes generation.`);

    // Always attempt to schedule the action when the transcript changes.
    // Debouncing/rate-limiting will be handled elsewhere if needed.
    console.log(`[Trigger meeting_notes] Scheduling AI notes generation for fileId ${fileId}...`);
    // Use the top-level imported api

    try {
      // Schedule the updateAiNotesFromTranscript action to run after 5 seconds
      const newAiNotesJobId = await ctx.scheduler.runAfter(
        5000, // 5 seconds (reduced from 30 seconds)
        api.actions.meetingNoteActions.updateAiNotesFromTranscript,
        { fileId: fileId.toString() }
      );

      console.log(`[Trigger meeting_notes] Scheduled job ${newAiNotesJobId} for fileId ${fileId}.`);

      // We are no longer setting the flag/jobId here in the trigger.
      // The action itself will handle clearing flags upon completion.

    } catch (error) {
      console.error(`[Trigger meeting_notes] Error scheduling job for fileId ${fileId}:`, error);
    }
  } else {
    console.log(`[Trigger meeting_notes] No relevant transcript update detected for ID: ${change.id}.`);
  }
});

// Register a trigger for the "people" table
triggers.register("people", async (ctx, change) => {
  // Check if it's a new person with a description/research or if the description/research has been updated
  const isDescriptionUpdate = 
    (change.operation === "insert" && change.newDoc?.description !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.description !== change.newDoc?.description &&
     change.newDoc?.description !== undefined);
  
  const isResearchUpdate = 
    (change.operation === "insert" && change.newDoc?.research !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.research !== change.newDoc?.research &&
     change.newDoc?.research !== undefined);

  if (isDescriptionUpdate || isResearchUpdate) {
    console.log("Person description or research changed, scheduling short description generation with debouncing");

    // Combine description and research for source text (if both exist)
    let sourceText = "";
    if (change.newDoc?.description) {
      sourceText += change.newDoc.description;
    }
    if (change.newDoc?.research) {
      if (sourceText) sourceText += "\n\n";
      sourceText += change.newDoc.research;
    }

    // Use the scheduleFieldGeneration helper with debouncing
    await scheduleFieldGeneration(
      ctx,
      "people",                // Table name
      change.id,               // Document ID
      sourceText,              // Source text
      "short_description",     // Target field
      "person-short-description", // Prompt slug
      "shortDescriptionJobId", // Field to store the job ID for debouncing
      SHORT_DESCRIPTION_DEBOUNCE_DELAY // Use the standard delay
    );
  }
});

// Register a trigger for the "organizations" table
triggers.register("organizations", async (ctx, change) => {
  // Check if it's a new organization with a description/research or if the description/research has been updated
  const isDescriptionUpdate = 
    (change.operation === "insert" && change.newDoc?.description !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.description !== change.newDoc?.description &&
     change.newDoc?.description !== undefined);
  
  const isResearchUpdate = 
    (change.operation === "insert" && change.newDoc?.research !== undefined) ||
    (change.operation === "update" &&
     change.oldDoc?.research !== change.newDoc?.research &&
     change.newDoc?.research !== undefined);

  if (isDescriptionUpdate || isResearchUpdate) {
    console.log("Organization description or research changed, scheduling short description generation with debouncing");

    // Combine description and research for source text (if both exist)
    let sourceText = "";
    if (change.newDoc?.description) {
      sourceText += change.newDoc.description;
    }
    if (change.newDoc?.research) {
      if (sourceText) sourceText += "\n\n";
      sourceText += change.newDoc.research;
    }

    // Use the scheduleFieldGeneration helper with debouncing
    await scheduleFieldGeneration(
      ctx,
      "organizations",         // Table name
      change.id,               // Document ID
      sourceText,              // Source text
      "short_description",     // Target field
      "organization-short-description", // Prompt slug
      "shortDescriptionJobId", // Field to store the job ID for debouncing
      SHORT_DESCRIPTION_DEBOUNCE_DELAY // Use the standard delay
    );
  }
});

// --- END NEW TRIGGERS ---


/**
 * A wrapper around Convex's query function that uses Zod for validation
 * This provides better type safety and more flexible validation
 */
export const zQuery = zCustomQuery(query, NoOp);

/**
 * A wrapper around Convex's mutation function that uses Zod for validation
 * This provides better type safety and more flexible validation
 *
 * Now wrapped with triggers.wrapDB to enable automatic trigger execution
 */
export const zMutation = zCustomMutation(mutation, customCtx(triggers.wrapDB));

/**
 * A wrapper around Convex's action function that uses Zod for validation
 * This provides better type safety and more flexible validation
 */
export const zAction = zCustomAction(action, NoOp);

/**
 * A wrapper around Convex's internalQuery function that uses Zod for validation
 * This provides better type safety and more flexible validation
 */
export const zInternalQuery = zCustomQuery(internalQuery, NoOp);

import {
  internalQuery
} from '../_generated/server';
import { Doc } from '../_generated/dataModel';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery, zInternalQuery } from '../functions';
import { getSubTableData } from '../utils/fileUtils';

// Import all schemas from the centralized schema file
import {
  FileSchema,
  IntegrationSchema,
  AttendeeDetailsSchema,
  RelatedEntitySchema,
  SubTableDataSchema,
  GetFileRecordBasicArgsSchema,
  GetFileArgsSchema,
  GetFileUrlArgsSchema,
  GetFileByStorageIdArgsSchema,
  GetFileByBoxIdArgsSchema,
  GetRecentlyModifiedFilesArgsSchema,
  GetAttendeeDetailsArgsSchema,
  GetRelatedEntitiesArgsSchema,
  GetSubTableDataArgsSchema,
} from './fileStorageSchema';

/**
 * Get a basic file record by its ID.
 *
 * @example
 * const file = await client.query.getFileRecordBasic({ fileId: "files:abc123" });
 * if (file) {
 *   console.log(`File title: ${file.title}`);
 * }
 */
export const getFileRecordBasic = zQuery({
  args: GetFileRecordBasicArgsSchema.shape,
  returns: FileSchema.nullable(),
  handler: async (ctx, args) => {
    try {
      return await ctx.db.get(args.fileId);
    } catch (error) {
      console.error(`Error fetching file record: ${error}`);
      return null;
    }
  },
});

/**
 * Internal query to get a file by ID
 *
 * @example
 * const file = await ctx.runQuery(internal.files.fileStorageQueries.getFile, { fileId: "files:abc123" });
 */
export const getFile = zInternalQuery({
  args: GetFileArgsSchema.shape,
  returns: FileSchema.nullable(),
  handler: async (ctx, args) => {
    return await ctx.db.get(args.fileId);
  },
});

/**
 * Get file URL for download
 *
 * @example
 * const url = await client.query.getFileUrl({ fileId: "storage:abc123" });
 * console.log(`File download URL: ${url}`);
 */
export const getFileUrl = zQuery({
  args: GetFileUrlArgsSchema.shape,
  returns: z.string().nullable(),
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      // First verify the storage ID exists in the system table
      const storageExists = await ctx.db.system.get(fileId);
      if (!storageExists) {
        throw new Error("Storage file not found in system");
      }

      return await ctx.storage.getUrl(fileId);
    } catch (error) {
      console.error(`Error getting file URL: ${error}`);
      throw new Error(`Failed to get URL for file: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});

/**
 * Get a file by its storage ID
 *
 * @example
 * const file = await client.query.getFileByStorageId({ storageId: "storage:abc123" });
 * if (file) {
 *   console.log(`Found file: ${file.title}`);
 * }
 */
export const getFileByStorageId = zQuery({
  args: GetFileByStorageIdArgsSchema.shape,
  returns: FileSchema.nullable(),
  handler: async (ctx, args) => {
    const { storageId } = args;

    try {
      // Query files by fileStorageId
      const file = await ctx.db
        .query('files')
        .filter(q => q.eq(q.field('fileStorageId'), storageId))
        .first();

      return file;
    } catch (error) {
      console.error(`Error getting file by storage ID: ${error}`);
      return null;
    }
  }
});

/**
 * Get a file by its Box file ID
 *
 * @example
 * const file = await ctx.runQuery(internal.files.fileStorageQueries.getFileByBoxId, { boxFileId: "box123" });
 */
export const getFileByBoxId = zInternalQuery({
  args: GetFileByBoxIdArgsSchema.shape,
  returns: FileSchema.nullable(),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("files")
      .filter((q) => q.eq(q.field("box_file_id"), args.boxFileId))
      .first();
  },
});

/**
 * Get recently modified files
 *
 * This query retrieves the most recently modified files across all document types,
 * sorted by updated_at timestamp in descending order.
 *
 * @example
 * const recentFiles = await client.query.getRecentlyModifiedFiles({ limit: 10 });
 * console.log(`Found ${recentFiles.length} recently modified files`);
 */
export const getRecentlyModifiedFiles = zQuery({
  args: GetRecentlyModifiedFilesArgsSchema.shape,
  returns: z.array(FileSchema),
  handler: async (ctx, args) => {
    const { limit } = args;

    try {
      // Query files and sort by updated_at in descending order
      const recentFiles = await ctx.db
        .query('files')
        .order('desc')
        .collect();

      // Sort manually by updated_at field and take the specified limit
      return recentFiles
        .sort((a, b) => (b.updated_at || 0) - (a.updated_at || 0))
        .slice(0, limit);
    } catch (error) {
      console.error(`Error getting recently modified files: ${error}`);
      return [];
    }
  }
});

/**
 * Helper function to parse attendee IDs and separate them by type
 */
function parseAttendeeIds(attendeeIds: string[]) {
  const peopleIds: string[] = [];
  const organizationIds: string[] = [];
  
  for (const id of attendeeIds) {
    if (id.startsWith('people:')) {
      peopleIds.push(id);
    } else if (id.startsWith('organizations:')) {
      organizationIds.push(id);
    } else {
      console.warn(`Unknown attendee ID format: ${id}`);
    }
  }
  
  return { peopleIds, organizationIds };
}

/**
 * Helper function to get people details with their organization memberships
 */
async function getPeopleDetails(ctx: any, peopleIds: string[]) {
  if (peopleIds.length === 0) return [];

  const peopleDetailsPromises = peopleIds.map(async (personId) => {
    try {
      const person = await ctx.db.get(personId as any);
      if (!person) {
        console.warn(`Person ${personId} not found`);
        return null;
      }

      // Fetch organization relationships for this person
      const relationships = await ctx.db
        .query('relationships')
        .filter((q: any) => 
          q.and(
            q.eq(q.field('source_type'), 'person'),
            q.eq(q.field('source_id'), person._id)
          )
        )
        .collect();

      // Fetch the actual organization documents
      const orgDetailsPromises = relationships.map(async (rel: any) => {
        try {
          const org = await ctx.db.get(rel.target_id as any);
          return org && org._id.toString().startsWith('organizations:') ? org : null;
        } catch {
          return null;
        }
      });
      
      const orgDocs = (await Promise.all(orgDetailsPromises)).filter(org => org !== null);
      const organizations = orgDocs.map(org => ({ 
        id: org._id, 
        name: org.name || 'Unknown Organization' 
      }));

      // Generate initials from person name
      const nameParts = (person.name || '').split(' ');
      const initials = nameParts.length > 1
        ? `${nameParts[0][0] || ''}${nameParts[nameParts.length - 1][0] || ''}`.toUpperCase()
        : (person.name?.[0] || '?').toUpperCase();

      return {
        id: person._id,
        type: 'person' as const,
        name: person.name ?? 'Unknown Person',
        image: person.image,
        initials: initials,
        email: person.email,
        organizations: organizations
      };
    } catch (error) {
      console.warn(`Error fetching person ${personId}:`, error);
      return null;
    }
  });

  const results = await Promise.all(peopleDetailsPromises);
  return results.filter((result): result is NonNullable<typeof result> => result !== null);
}

/**
 * Helper function to get organization details
 */
async function getOrganizationDetails(ctx: any, organizationIds: string[]) {
  if (organizationIds.length === 0) return [];

  const orgDetailsPromises = organizationIds.map(async (orgId) => {
    try {
      const org = await ctx.db.get(orgId as any);
      if (!org) {
        console.warn(`Organization ${orgId} not found`);
        return null;
      }

      // Generate initials from organization name
      const nameParts = (org.name || '').split(' ');
      const initials = nameParts.length > 1
        ? `${nameParts[0][0] || ''}${nameParts[1]?.[0] || ''}`.toUpperCase()
        : (org.name?.[0] || '?').toUpperCase();

      return {
        id: org._id,
        type: 'organization' as const,
        name: org.name ?? 'Unknown Organization',
        image: undefined,
        initials: initials,
        email: org.email,
        organizations: [] // Organizations don't have organization memberships
      };
    } catch (error) {
      console.warn(`Error fetching organization ${orgId}:`, error);
      return null;
    }
  });

  const results = await Promise.all(orgDetailsPromises);
  return results.filter((result): result is NonNullable<typeof result> => result !== null);
}

/**
 * Get attendee details for meeting notes
 *
 * This query fetches the details of attendees (people or organizations) for a meeting note,
 * including organization memberships for people.
 *
 * @example
 * const attendees = await client.query.getAttendeeDetails({
 *   attendeeIds: ["people:abc123", "organizations:def456"]
 * });
 * console.log(`Found ${attendees.length} attendees`);
 */
export const getAttendeeDetails = zQuery({
  args: GetAttendeeDetailsArgsSchema.shape,
  handler: async (ctx, args) => {
    const { attendeeIds } = args;

    try {
      if (!attendeeIds || attendeeIds.length === 0) return [];

      // Parse and separate IDs by type
      const { peopleIds, organizationIds } = parseAttendeeIds(attendeeIds);

      // Fetch details in parallel
      const [peopleDetails, orgDetails] = await Promise.all([
        getPeopleDetails(ctx, peopleIds),
        getOrganizationDetails(ctx, organizationIds)
      ]);

      // Combine and sort results
      const allAttendees = [...peopleDetails, ...orgDetails];
      return allAttendees.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error(`Error getting attendee details: ${error}`);
      return [];
    }
  }
});

/**
 * Get all entities related to a specific file ID via file_relationships
 *
 * @example
 * const relatedEntities = await client.query.getRelatedEntities({ fileId: "files:abc123" });
 * console.log(`Found ${relatedEntities.length} related entities`);
 */
export const getRelatedEntities = zQuery({
  args: GetRelatedEntitiesArgsSchema.shape,
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      // Get all relationships for this file
      const relationships = await ctx.db
        .query('file_relationships')
        .withIndex('by_file', q => q.eq('file_id', fileId))
        .collect();

      // Group relationships by subject type
      const entityIdsByType: Record<string, any[]> = {};
      for (const rel of relationships) {
        if (!entityIdsByType[rel.subject_type]) {
          entityIdsByType[rel.subject_type] = [];
        }
        entityIdsByType[rel.subject_type].push(rel.subject_id);
      }

      // Fetch entity details for each type
      const promises = Object.entries(entityIdsByType).map(async ([type, ids]) => {
        try {
          const documents = await Promise.all(ids.map(id => ctx.db.get(id as any)));
          return documents
            .filter((doc): doc is NonNullable<typeof doc> => doc !== null)
            .map(doc => ({
              _id: doc._id,
              type: type,
              name: (doc as any).title ?? (doc as any).name ?? 'Unknown Name'
            }));
        } catch (error) {
          console.error(`Error fetching details for type ${type}:`, error);
          return [];
        }
      });

      const resultsByType = await Promise.all(promises);
      return resultsByType.flat();
    } catch (error) {
      console.error(`Error getting related entities: ${error}`);
      return [];
    }
  },
});

/**
 * Get integrations, specifically the Box integration
 *
 * @example
 * const boxIntegration = await client.query.getIntegrations();
 * if (boxIntegration) {
 *   console.log(`Box integration enabled: ${boxIntegration.enabled}`);
 * }
 */
export const getIntegrations = zQuery({
    args: {},
    returns: IntegrationSchema.nullable(),
    handler: async (ctx) => {
        const integrations = await ctx.db.query("integrations").collect();
        return integrations.find(integration => 
            integration.immutable_slug === "box"
        ) || null;
    },
});

/**
 * Public query wrapper for getting sub-table data.
 *
 * @example
 * const data = await client.query.getSubTableDataPublic({
 *   fileId: "files:abc123",
 *   docType: "KNOWLEDGE_BASE"
 * });
 * console.log(`Category: ${data.category}, Content: ${data.content?.substring(0, 50)}...`);
 */
export const getSubTableDataPublic = zQuery({
  args: GetSubTableDataArgsSchema.shape,
  returns: SubTableDataSchema,
  handler: async (ctx, args) => {
    try {
      // Use the imported function directly and handle undefined -> null conversion
      const result = await getSubTableData(ctx, args.fileId, args.docType);
      return {
        category: result.category ?? null,
        content: result.content ?? null,
      };
    } catch (error) {
      console.error(`Error getting sub-table data: ${error}`);
      return { category: null, content: null };
    }
  },
});

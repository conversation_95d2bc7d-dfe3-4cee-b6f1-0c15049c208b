import {
  mutation, internalMutation, type MutationCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { ConvexError } from 'convex/values';
import { api } from '../_generated/api';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zMutation } from '../functions';
import { scheduleDebouncedJob } from '../utils/schedulerUtils';
import { SHORT_DESCRIPTION_DEBOUNCE_DELAY } from '../utils/shortDescriptionUtils';
import { TRIEVE_OPERATION_DELAY } from '../utils/trieveUtils';
import {
  UpdateMeetingNoteArgsSchema as ZodUpdateMeetingNoteArgs,
  DeleteMeetingNoteArgsSchema as ZodDeleteMeetingNoteArgs,
  CreateMeetingNoteArgsSchema as ZodCreateMeetingNoteArgs,
} from './meetingNotesSchema';
import { type RelationshipLink } from '../../zod/files-schema';

async function scheduleMeetingNoteShortDescription(
  ctx: MutationCtx,
  fileId: Id<'files'>,
  logPrefix = ''
): Promise<void> {
  try {
    // Get the meeting note to combine AI and manual content
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q: any) => q.eq('fileId', fileId))
      .first();

    if (!mnRecord) {
      console.log(`${logPrefix}Meeting note record not found for file ID ${fileId}`);
      return;
    }

    const aiContent = mnRecord.content || "";
    const manualContent = mnRecord.manualNotes || "";

    // Combine the content with a separator
    let combinedContent = "";
    if (aiContent && manualContent) {
      combinedContent = `AI-ASSISTED NOTES:\n${aiContent}\n\nMANUAL NOTES:\n${manualContent}`;
    } else if (aiContent) {
      combinedContent = aiContent;
    } else {
      combinedContent = manualContent;
    }

    // Only schedule if there's content to process
    if (combinedContent.trim().length > 0) {
      await scheduleDebouncedJob(ctx, {
        tableName: "files",
        documentId: fileId,
        jobIdFieldName: "shortDescriptionJobId",
        jobToSchedule: api.actions.orchestrators.generateAndStoreField,
        jobArgs: {
          tableName: "files",
          documentId: fileId.toString(),
          sourceText: combinedContent,
          targetFieldName: "short_description",
          promptSlug: "meeting-summary"
        },
        delayMs: SHORT_DESCRIPTION_DEBOUNCE_DELAY,
      });
      console.log(`${logPrefix}Successfully scheduled short description generation for Meeting Note: ${fileId}`);
    } else {
      console.log(`${logPrefix}Combined content is empty for file ${fileId}, skipping summary generation.`);
    }
  } catch (error) {
    console.error(`${logPrefix}Failed to schedule short description generation for Meeting Note ${fileId}:`, error);
    // Don't throw the error, just log it to prevent blocking the main operation
  }
}

/**
 * Create a new Meeting Note
 */
export const createMeetingNote = zMutation({
  args: ZodCreateMeetingNoteArgs.shape, // Use imported Zod schema
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("User must be authenticated to create a meeting note.");
    }
    const now = Date.now();

    // 1. Create the record in the 'files' table
    const fileId = await ctx.db.insert('files', {
      title: args.title,
      docType: 'MEETING_NOTES',
      ownerId: args.ownerId,
      updated_at: now,
    });

    // 2. Create the corresponding record in the 'meeting_notes' table
    await ctx.db.insert('meeting_notes', {
      fileId: fileId,
      content: args.content,
      meetingDate: args.meetingDate,
      attendees: args.attendees,
      category: args.category,
      updated_at: now,
    });

    // 3. Handle initial relationships
    if (args.initialRelationships && args.initialRelationships.length > 0) {
      await Promise.all(args.initialRelationships.map(link =>
        ctx.db.insert('file_relationships', {
          file_id: fileId,
          subject_type: link.subject_type,
          subject_id: link.subject_id,
          linked_at: now,
        })
      ));
    }

    // --- Trigger Trieve Ingestion ---
    // TODO: Determine how to get the correct orgId for multi-tenancy filtering
    const orgId = "PLACEHOLDER_ORG_ID"; // Replace with actual orgId logic
    // TODO: Determine if projectId is relevant/available here
    const projectId = undefined; // Replace with actual projectId if available

    const docDataForTrieve = {
      id: fileId,
      title: args.title,
      content: args.content ?? "", // Ensure content is not null/undefined
      docType: 'MEETING_NOTES',
      projectId: projectId,
      orgId: orgId,
      createdAt: now, // Use the creation timestamp
      // Pass category if available in args
      category: args.category,
    };

    // Fetch necessary fields for the new chunking strategy
    // Note: short_description might be null/undefined on creation
    const fileRecord = await ctx.db.get(fileId); // Get the just-created file record

    const combinedDocData = {
      id: fileId,
      title: args.title,
      short_description: fileRecord?.short_description ?? null, // Fetch from file record
      content: args.content ?? "", // Ensure content is not null/undefined
      category: args.category ?? null, // Use category from args
      docType: 'MEETING_NOTES',
      projectId: projectId,
      orgId: orgId,
      createdAt: now, // Use the creation timestamp
    };

    try {
      // Schedule the upsert action to run immediately after mutation completes
      await ctx.scheduler.runAfter(TRIEVE_OPERATION_DELAY, api.actions.trieveActions.trieveUpsertDocumentChunks, { doc: combinedDocData });
      console.log(`Successfully scheduled Trieve upsert for Meeting Note: ${fileId}`);
    } catch (error) {
      console.error(`Failed to schedule Trieve upsert for Meeting Note ${fileId}:`, error);
      // Decide if this error should block the mutation result or just be logged
    }
    // --- End Trieve Indexing ---

    // --- Schedule Short Description Generation using Debounce Utility ---
    // Only schedule if content was provided
    if (args.content) {
      await scheduleMeetingNoteShortDescription(ctx, fileId, "[createMeetingNote] ");
    }
    // --- End Short Description Generation ---


    // 4. Return the ID of the new file record
    return fileId;
  },
});

/**
 * Append a transcript segment to a meeting note's transcript field
 * This mutation is called by the RealtimeTranscriptEditor component when it receives
 * a completed transcription segment from OpenAI's Realtime API.
 */
export const appendTranscriptSegment = mutation({
  args: {
    fileId: v.id('files'),
    segmentText: v.string()
  },
  handler: async (ctx, args) => {
    const { fileId, segmentText } = args;

    // Find the meeting_notes record for this file
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q) => q.eq('fileId', fileId))
      .first();

    if (!mnRecord) {
      console.error(`[appendTranscriptSegment] Meeting note record not found for file ID ${fileId}`);
      throw new ConvexError(`Meeting note record not found for file ID ${fileId}`);
    }

    // Get the current transcript or initialize as empty string
    const currentTranscript = mnRecord.transcript || "";

    // Append the new segment with double newlines
    const updatedTranscript = currentTranscript + segmentText + "\n\n";

    // Update the meeting note with the new transcript
    await ctx.db.patch(mnRecord._id, {
      transcript: updatedTranscript,
      updated_at: Date.now()
    });

    // Only schedule AI notes generation for longer segments to avoid too many jobs
    // This reduces the number of concurrent AI jobs during recording
    if (segmentText.length > 50) {
      try {
        await ctx.scheduler.runAfter(
          5000, // 5 seconds delay
          api.actions.meetingNoteActions.updateAiNotesFromTranscript,
          { fileId: fileId.toString() }
        );
        console.log(`[appendTranscriptSegment] Scheduled AI notes generation for fileId ${fileId}`);
      } catch (error) {
        console.error(`[appendTranscriptSegment] Error scheduling AI notes generation for fileId ${fileId}:`, error);
      }
    } else {
      console.log(`[appendTranscriptSegment] Skipping AI notes generation for short segment (${segmentText.length} chars)`);
    }

    return { success: true };
  }
});

export const updateMeetingNote = zMutation({
  args: ZodUpdateMeetingNoteArgs.shape,
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("User must be authenticated to update a meeting note.");
    }

    const now = Date.now();
    const typedFileId = args.fileId as Id<"files">;

    // 1. Fetch existing file record BEFORE updates to get original creation time
    const fileRecord = await ctx.db.get(typedFileId);
    if (!fileRecord) {
      throw new ConvexError(`File record ${typedFileId} not found.`);
    }

    // 2. Update the 'files' table record with all fields at once
    const fileUpdateData: Partial<Doc<'files'>> = {
      updated_at: now
    };

    if (args.title !== undefined) {
      fileUpdateData.title = args.title;
    }

    await ctx.db.patch(typedFileId, fileUpdateData);

    // 3. Update the 'meeting_notes' table record
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q) => q.eq('fileId', typedFileId))
      .first();

    if (!mnRecord) {
      throw new ConvexError(`Meeting note entry not found for fileId: ${typedFileId}`);
    }

    const mnUpdateData: Partial<Doc<'meeting_notes'>> = {
      updated_at: now
    };

    if (args.content !== undefined) mnUpdateData.content = args.content;
    if (args.manualNotes !== undefined) mnUpdateData.manualNotes = args.manualNotes;
    if (args.transcript !== undefined) mnUpdateData.transcript = args.transcript;
    if (args.meetingDate !== undefined) mnUpdateData.meetingDate = args.meetingDate;
    if (args.attendees !== undefined) mnUpdateData.attendees = args.attendees;
    if (args.category !== undefined) mnUpdateData.category = args.category;

    // Log before updating meeting_notes record
    console.log(`Updating meeting_notes record ${mnRecord._id} with content: ${args.content ? 'content provided' : 'no content update'}`);

    // Transcript updates are now handled by the trigger in functions.ts
    if (args.transcript !== undefined) {
      console.log(`Transcript update detected for meeting note ${typedFileId}`);
      // The trigger will handle scheduling the AI notes generation
    }

    await ctx.db.patch(mnRecord._id, mnUpdateData);

    // Log after updating meeting_notes record
    console.log(`Updated meeting_notes record ${mnRecord._id}`);

    // --- Schedule Debounced Jobs ---
    if (args.content !== undefined || args.manualNotes !== undefined) {
      // Schedule Short Description Generation using our helper function
      await scheduleMeetingNoteShortDescription(ctx, typedFileId, "[updateMeetingNote] ");

      // Schedule Trieve Re-indexing
      try {
        await scheduleDebouncedJob(ctx, {
          tableName: "files",
          documentId: typedFileId,
          jobIdFieldName: "trieveIndexingJobId",
          jobToSchedule: api.actions.trieveActions.trieveUpsertDocumentChunks,
          jobArgs: { fileId: typedFileId }, // Pass fileId to the action
          delayMs: TRIEVE_OPERATION_DELAY,
        });
        console.log(`Successfully scheduled debounced Trieve re-indexing for Meeting Note: ${typedFileId}`);
      } catch (error) {
        console.error(`Failed to schedule debounced Trieve re-indexing for Meeting Note ${typedFileId}:`, error);
      }
    } else if (args.title !== undefined) {
      // If only title changed, still schedule Trieve re-indexing
       try {
        await scheduleDebouncedJob(ctx, {
          tableName: "files",
          documentId: typedFileId,
          jobIdFieldName: "trieveIndexingJobId",
          jobToSchedule: api.actions.trieveActions.trieveUpsertDocumentChunks,
          jobArgs: { fileId: typedFileId }, // Pass fileId to the action
          delayMs: TRIEVE_OPERATION_DELAY,
        });
        console.log(`Successfully scheduled debounced Trieve re-indexing (title change) for Meeting Note: ${typedFileId}`);
      } catch (error) {
        console.error(`Failed to schedule debounced Trieve re-indexing (title change) for Meeting Note ${typedFileId}:`, error);
      }
    }
    // --- End Schedule Debounced Jobs ---


    // 4. Add new relationships
    if (args.relationshipsToAdd && args.relationshipsToAdd.length > 0) {
      await Promise.all(
        args.relationshipsToAdd.map(async (link: RelationshipLink) => {
          const existing = await ctx.db.query('file_relationships')
            .withIndex('by_subject_type', q => q.eq('subject_type', link.subject_type))
            .filter(q => q.and(q.eq(q.field('subject_id'), link.subject_id), q.eq(q.field('file_id'), typedFileId)))
            .first();
          if (!existing) {
            await ctx.db.insert('file_relationships', {
              file_id: typedFileId,
              subject_type: link.subject_type,
              subject_id: link.subject_id,
              linked_at: now,
              featuredFile: false
            });
          }
        })
      );
    }

    // 5. Remove relationships
    if (args.relationshipsToRemove && args.relationshipsToRemove.length > 0) {
      const relationshipRecords = await Promise.all(
        args.relationshipsToRemove.map(async (linkToRemove: RelationshipLink) => {
          return await ctx.db.query('file_relationships')
            .withIndex('by_subject_type', q => q.eq('subject_type', linkToRemove.subject_type))
            .filter(q => q.and(q.eq(q.field('subject_id'), linkToRemove.subject_id), q.eq(q.field('file_id'), typedFileId)))
            .first();
        })
      );
      const recordsToDelete = relationshipRecords.filter((r): r is Doc<'file_relationships'> => r !== null);
      await Promise.all(recordsToDelete.map((record) => ctx.db.delete(record._id)));
    }

    // Trieve reindexing is now handled by the debounced job scheduling above

    return { success: true };
  }
});

/**
 * Delete a Meeting Note
 */
export const deleteMeetingNote = zMutation({
  args: ZodDeleteMeetingNoteArgs.shape, // Use imported Zod schema
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("User must be authenticated to delete a meeting note.");
    }

    const { fileId } = args;

    // 1. Delete associated relationships
    const relationshipRecords = await ctx.db
      .query('file_relationships')
      .withIndex('by_file', (q) => q.eq('file_id', fileId))
      .collect();
    await Promise.all(relationshipRecords.map(record => ctx.db.delete(record._id)));

    // 2. Delete the corresponding meeting_notes record
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q) => q.eq('fileId', fileId))
      .first();

    if (mnRecord) {
      await ctx.db.delete(mnRecord._id);
    } else {
      console.warn(`Meeting note record not found for fileId ${fileId} during deletion.`);
    }

    // 3. Delete the main file record
    await ctx.db.delete(fileId);

    return { success: true };
  },
});

/**
 * Internal mutation to update the content of a meeting_notes record.
 * This is callable by actions without user authentication context.
 */
export const internalUpdateMeetingNoteContent = internalMutation({
  args: {
    fileId: v.id('files'),
    content: v.string(),
  },
  handler: async (ctx, args: { fileId: Id<'files'>, content: string }) => {
    const { fileId, content } = args;

    // Get the meeting_notes record
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q) => q.eq('fileId', fileId))
      .first();

    if (!mnRecord) {
      throw new ConvexError(`[Internal Mutation internalUpdateMeetingNoteContent] Meeting note entry not found for fileId: ${fileId}`);
    }

    console.log(`[Internal Mutation internalUpdateMeetingNoteContent] Updating content for meeting_notes record ${mnRecord._id} (fileId: ${fileId})`);

    try {
      await ctx.db.patch(mnRecord._id, {
        content: content,
        updated_at: Date.now()
      });
      console.log(`[Internal Mutation internalUpdateMeetingNoteContent] Successfully updated content for meeting_notes record ${mnRecord._id}`);

      // Now, schedule the summary generation since content was updated
      await scheduleMeetingNoteShortDescription(ctx, fileId, "[internalUpdateMeetingNoteContent] ");

      return { success: true };
    } catch (patchError) {
      console.error(`[Internal Mutation internalUpdateMeetingNoteContent] Error patching meeting_notes record ${mnRecord._id}:`, patchError);
      throw patchError;
    }
  },
});

/**
 * Internal mutation to clear scheduling flags in the meeting_notes record.
 * This is used by the updateAiNotesFromTranscript action after generating notes.
 */
export const internalUpdateNotesAndClearScheduleFlag = internalMutation({
  args: {
    fileId: v.id('files'),
  },
  handler: async (ctx, args: { fileId: Id<'files'> }) => {
    const { fileId } = args;

    // Get the meeting_notes record
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q) => q.eq('fileId', fileId))
      .first();

    if (!mnRecord) {
      throw new ConvexError(`Meeting note entry not found for fileId: ${fileId}`);
    }

    console.log(`[Internal Mutation internalUpdateNotesAndClearScheduleFlag] Attempting to clear flags for meeting_notes record ${mnRecord._id} (associated with fileId: ${fileId})`);

    try {
      // Update the meeting_notes record to clear flags
      await ctx.db.patch(mnRecord._id, {
        isAiNotesUpdateScheduled: false,
        aiNotesJobId: undefined,
        updated_at: Date.now()
      });

      console.log(`[Internal Mutation internalUpdateNotesAndClearScheduleFlag] Successfully patched flags for meeting_notes record ${mnRecord._id}`);
    } catch (patchError) {
      console.error(`[Internal Mutation internalUpdateNotesAndClearScheduleFlag] Error patching meeting_notes record ${mnRecord._id}:`, patchError);
      // Re-throw the error so the calling action knows it failed
      throw patchError;
    }

    return { success: true };
  },
});

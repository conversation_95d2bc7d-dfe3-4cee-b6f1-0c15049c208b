import {
  mutation, internalMutation, type MutationCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { ConvexError } from 'convex/values';
import { api } from '../_generated/api';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zMutation } from '../functions';

// Import schemas from the centralized schema file
import {
  SaveFileMetadataArgsSchema,
  FileResponseSchema,
} from './fileStorageSchema';

/**
 * Generate a URL for file upload
 *
 * @example
 * const uploadUrl = await client.mutation.generateUploadUrl();
 * // Use uploadUrl with fetch to upload a file
 */
export const generateUploadUrl = zMutation({
  args: {},
  returns: z.string(),
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  }
});

/**
 * Save file metadata after upload
 *
 * @example
 * const { fileId } = await client.mutation.saveFileMetadata({
 *   name: "My Document",
 *   docType: "DOCUMENT",
 *   size: 1024
 * });
 */
export const saveFileMetadata = zMutation({
  args: SaveFileMetadataArgsSchema.shape,
  returns: FileResponseSchema,
  handler: async (ctx, args) => {
    const { name, docType, size, description, ownerId, parentEntityId, fileStorageId, fileFilename, fileExtension, fileHash } = args;

    try {
      // Create the file record
      const fileId = await ctx.db.insert('files', {
        title: name, // Map incoming 'name' arg to 'title' field
        docType,
        fileSize: size,
        description,
        ownerId,
        parentEntityId,
        fileStorageId,
        fileFilename,
        fileExtension,
        fileHash,
        uploadStatus: 'completed',
        updated_at: Date.now()
      });

      return { fileId };
    } catch (error) {
      console.error(`Error saving file metadata: ${error}`);
      throw new ConvexError({
        message: error instanceof Error ? error.message : "Unknown error saving file metadata",
        code: "SAVE_FILE_METADATA_ERROR"
      });
    }
  }
});

// Delete file and its metadata
export const deleteFile = mutation({
  args: {
    fileId: v.id('_storage'),
    documentId: v.optional(v.id('files'))
  },
  handler: async (ctx, args) => {
    const { fileId, documentId } = args;

    // Delete the file from storage
    await ctx.storage.delete(fileId);

    // If we have a document ID, delete the file record
    if (documentId) {
      await ctx.db.delete(documentId);
    }

    return true;
  }
});

export const deleteFileAndRelationships = internalMutation({
  args: {
    fileId: v.id("files"),
  },
  handler: async (ctx, args) => {
    // Get the file record first to access the storage ID
    const file = await ctx.db.get(args.fileId);
    if (!file) {
      console.warn(`File ${args.fileId} not found, skipping deletion`);
      return;
    }
    
    // Delete the actual file from Convex storage if it exists
    if (file.fileStorageId) {
      try {
        await ctx.storage.delete(file.fileStorageId);
        console.log(`Deleted file from storage: ${file.fileStorageId}`);
      } catch (error) {
        console.error(`Failed to delete file from storage: ${file.fileStorageId}`, error);
        // Continue with database cleanup even if storage deletion fails
      }
    }
    
    // Delete the file record
    await ctx.db.delete(args.fileId);
    
    // Delete related documents
    const documents = await ctx.db
      .query("documents")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .collect();
    
    for (const doc of documents) {
      await ctx.db.delete(doc._id);
    }
    
    // Delete file relationships
    const relationships = await ctx.db
      .query("file_relationships")
      .withIndex("by_file", (q) => q.eq("file_id", args.fileId))
      .collect();
    
    for (const rel of relationships) {
      await ctx.db.delete(rel._id);
    }
  },
});

// Process file with AI
export const processFile = mutation({
  args: {
    fileId: v.id('files')
  },
  handler: async (ctx, args) => {
    const { fileId } = args;

    // Get the file record
    const file = await ctx.db.get(fileId);
    if (!file) {
      throw new Error('File not found');
    }

    try {
      // Update status to processing
      await ctx.db.patch(fileId, {
        uploadStatus: 'processing'
      });

      // TODO: Implement AI processing logic here
      const aiResult = {
        summary: "Sample summary",
        entities: ["entity1", "entity2"]
      };

      // Update with results
      await ctx.db.patch(fileId, {
        uploadStatus: 'completed',
        parsedData: aiResult
      });

      return aiResult;
    } catch (error) {
      // Update status to failed
      await ctx.db.patch(fileId, {
        uploadStatus: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
});

/**
 * Update a file's parsed data
 *
 * This mutation updates the parsedData field of a file record.
 * Used by the analyzeUpload action to store AI analysis results.
 */
export const updateFileParsedData = mutation({
  args: {
    fileId: v.id('files'),
    parsedData: v.object({
      summary: v.string(),
      analysis: v.string(),
      entities: v.optional(v.array(v.string())),
      maritalStatus: v.optional(v.string()),
      spouses: v.optional(v.array(v.object({
        name: v.optional(v.string()),
        dateOfBirth: v.optional(v.string()),
      }))),
      children: v.optional(v.array(v.object({
        name: v.optional(v.string()),
        dateOfBirth: v.optional(v.string()),
      }))),
      assets: v.optional(v.array(v.object({
        name: v.optional(v.string()),
        value: v.optional(v.number()),
        type: v.optional(v.string()),
        description: v.optional(v.string())
      })))
    }),
    uploadStatus: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const { fileId, parsedData, uploadStatus } = args;

    // Update the file record
    const updateData: Partial<Doc<'files'>> = {
      parsedData,
      ...(uploadStatus && { uploadStatus: uploadStatus as "processing" | "completed" | "failed" })
    };

    await ctx.db.patch(fileId, updateData);
    return fileId;
  }
});

/**
 * Updates a specific field of a file record.
 * This is a utility mutation used by actions to update file fields like short_description.
 */
export const updateFileField = mutation({ // Use standard mutation, not zMutation
  args: {
    id: v.id('files'), // Use v.id for Convex IDs
    field: v.string(),
    value: v.any() // Use v.any() for flexible value types
  },
  handler: async (ctx, args) => {
    try {
      const { id, field, value } = args;

      // Check if the file exists
      const file = await ctx.db.get(id);
      if (!file) {
        throw new ConvexError({
          message: `File with ID ${id} not found`,
          code: "NOT_FOUND"
        });
      }

      // Create an update object with the field and value
      const update: Partial<Doc<'files'>> = {
        [field]: value,
        updated_at: Date.now() // Also update the timestamp
      };

      // Update the file record
      await ctx.db.patch(id, update);

      // Return a simple success object or just void
      return {
        success: true,
        message: `File field '${field}' updated successfully`
      };
    } catch (error: unknown) {
      console.error("Error updating file field:", error);
      // Re-throw ConvexErrors, wrap others
      if (error instanceof ConvexError) throw error;

      throw new ConvexError({
        message: error instanceof Error ? error.message : "Unknown error occurred while updating file field",
        code: "UPDATE_FILE_FIELD_ERROR",
      });
    }
  }
});

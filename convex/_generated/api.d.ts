/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as actions_aiActions from "../actions/aiActions.js";
import type * as actions_aiCategorizeAction from "../actions/aiCategorizeAction.js";
import type * as actions_aiUtils from "../actions/aiUtils.js";
import type * as actions_box from "../actions/box.js";
import type * as actions_meetingNoteActions from "../files/meetingNoteActions.js";
import type * as actions_orchestrators from "../actions/orchestrators.js";
import type * as actions_parseDocumentAction from "../actions/parseDocumentAction.js";
import type * as actions_trieveActions from "../actions/trieveActions.js";
import type * as actions_writeTaskDescription from "../actions/writeTaskDescription.js";
import type * as ai_llmFriendlyConverter from "../ai/llmFriendlyConverter.js";
import type * as assignments from "../assignments.js";
import type * as auth from "../auth.js";
import type * as badges from "../badges.js";
import type * as bills from "../bills.js";
import type * as box_actions from "../box/actions.js";
import type * as box_index from "../box/index.js";
import type * as box_signatureMutations from "../box/signatureMutations.js";
import type * as box_signatures from "../box/signatures.js";
import type * as clients_clientMutations from "../clients/clientMutations.js";
import type * as clients_clientQueries from "../clients/clientQueries.js";
import type * as crons from "../crons.js";
import type * as dashboard from "../dashboard.js";
import type * as databasecleanup from "../databasecleanup.js";
import type * as decisions from "../decisions.js";
import type * as directory_directory from "../directory/directory.js";
import type * as directory_directoryActions from "../directory/directoryActions.js";
import type * as directory_directoryOrganizations from "../directory/directoryOrganizations.js";
import type * as directory_directoryPeople from "../directory/directoryPeople.js";
import type * as directory_personTags from "../directory/personTags.js";
import type * as downloads from "../downloads.js";
import type * as entityFieldHelpers from "../entityFieldHelpers.js";
import type * as entity_relationships from "../entity_relationships.js";
import type * as files_clientQueries from "../files/clientQueries.js";
import type * as files_documentsMutations from "../files/documentsMutations.js";
import type * as files_documentsQueries from "../files/documentsQueries.js";
import type * as files_fileManagement from "../files/fileManagement.js";
import type * as files_fileStorageMutations from "../files/fileStorageMutations.js";
import type * as files_fileStorageQueries from "../files/fileStorageQueries.js";
import type * as files_files from "../files/files.js";
import type * as files_kbArticleMutations from "../files/kbArticleMutations.js";
import type * as files_kbArticleQueries from "../files/kbArticleQueries.js";
import type * as files_meetingNoteMutations from "../files/meetingNoteMutations.js";
import type * as files_meetingNoteQueries from "../files/meetingNoteQueries.js";
import type * as functions from "../functions.js";
import type * as generateEmbeddings from "../generateEmbeddings.js";
import type * as http from "../http.js";
import type * as integrations_billCom from "../integrations/billCom.js";
import type * as integrations_billComActions from "../integrations/billComActions.js";
import type * as integrations_integrations from "../integrations/integrations.js";
import type * as lineItems from "../lineItems.js";
import type * as openai_tools_openai_directory from "../openai_tools/openai_directory.js";
import type * as openai_tools_openai_projects from "../openai_tools/openai_projects.js";
import type * as openai_tools_openai_relationships from "../openai_tools/openai_relationships.js";
import type * as organizations from "../organizations.js";
import type * as people from "../people.js";
import type * as projectUpdates from "../projectUpdates.js";
import type * as projects from "../projects.js";
import type * as prompts from "../prompts.js";
import type * as relationships_fileRelationships from "../relationships/fileRelationships.js";
import type * as relationships_relationshipMutations from "../relationships/relationshipMutations.js";
import type * as relationships_relationshipQueries from "../relationships/relationshipQueries.js";
import type * as relationships_teams from "../relationships/teams.js";
import type * as relationships from "../relationships.js";
import type * as seeds_seedPrompts from "../seeds/seedPrompts.js";
import type * as seeds_seedRelationshipTypes from "../seeds/seedRelationshipTypes.js";
import type * as seeds_seed_badges from "../seeds/seed_badges.js";
import type * as seeds_seed_integrations from "../seeds/seed_integrations.js";
import type * as seeds_seed_people from "../seeds/seed_people.js";
import type * as seeds_seed_tagTypes_step1 from "../seeds/seed_tagTypes_step1.js";
import type * as seeds_seed_tags_step2 from "../seeds/seed_tags_step2.js";
import type * as semanticSearch from "../semanticSearch.js";
import type * as tags from "../tags.js";
import type * as tasks from "../tasks.js";
import type * as teams from "../teams.js";
import type * as trieve_deleteDocument from "../trieve/deleteDocument.js";
import type * as trieve_trieve from "../trieve/trieve.js";
import type * as universalSearch from "../universalSearch.js";
import type * as updateDocument from "../updateDocument.js";
import type * as users from "../users.js";
import type * as utils_fieldUtils from "../utils/fieldUtils.js";
import type * as utils_fileTypes from "../utils/fileTypes.js";
import type * as utils_fileUtils from "../utils/fileUtils.js";
import type * as utils_mentions from "../utils/mentions.js";
import type * as utils_schedulerUtils from "../utils/schedulerUtils.js";
import type * as utils_shortDescriptionUtils from "../utils/shortDescriptionUtils.js";
import type * as utils_trieveUtils from "../utils/trieveUtils.js";
import type * as welcome from "../welcome.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "actions/aiActions": typeof actions_aiActions;
  "actions/aiCategorizeAction": typeof actions_aiCategorizeAction;
  "actions/aiUtils": typeof actions_aiUtils;
  "actions/box": typeof actions_box;
  "actions/meetingNoteActions": typeof actions_meetingNoteActions;
  "actions/orchestrators": typeof actions_orchestrators;
  "actions/parseDocumentAction": typeof actions_parseDocumentAction;
  "actions/trieveActions": typeof actions_trieveActions;
  "actions/writeTaskDescription": typeof actions_writeTaskDescription;
  "ai/llmFriendlyConverter": typeof ai_llmFriendlyConverter;
  assignments: typeof assignments;
  auth: typeof auth;
  badges: typeof badges;
  bills: typeof bills;
  "box/actions": typeof box_actions;
  "box/index": typeof box_index;
  "box/signatureMutations": typeof box_signatureMutations;
  "box/signatures": typeof box_signatures;
  "clients/clientMutations": typeof clients_clientMutations;
  "clients/clientQueries": typeof clients_clientQueries;
  crons: typeof crons;
  dashboard: typeof dashboard;
  databasecleanup: typeof databasecleanup;
  decisions: typeof decisions;
  "directory/directory": typeof directory_directory;
  "directory/directoryActions": typeof directory_directoryActions;
  "directory/directoryOrganizations": typeof directory_directoryOrganizations;
  "directory/directoryPeople": typeof directory_directoryPeople;
  "directory/personTags": typeof directory_personTags;
  downloads: typeof downloads;
  entityFieldHelpers: typeof entityFieldHelpers;
  entity_relationships: typeof entity_relationships;
  "files/clientQueries": typeof files_clientQueries;
  "files/documentsMutations": typeof files_documentsMutations;
  "files/documentsQueries": typeof files_documentsQueries;
  "files/fileManagement": typeof files_fileManagement;
  "files/fileStorageMutations": typeof files_fileStorageMutations;
  "files/fileStorageQueries": typeof files_fileStorageQueries;
  "files/files": typeof files_files;
  "files/kbArticleMutations": typeof files_kbArticleMutations;
  "files/kbArticleQueries": typeof files_kbArticleQueries;
  "files/meetingNoteMutations": typeof files_meetingNoteMutations;
  "files/meetingNoteQueries": typeof files_meetingNoteQueries;
  functions: typeof functions;
  generateEmbeddings: typeof generateEmbeddings;
  http: typeof http;
  "integrations/billCom": typeof integrations_billCom;
  "integrations/billComActions": typeof integrations_billComActions;
  "integrations/integrations": typeof integrations_integrations;
  lineItems: typeof lineItems;
  "openai_tools/openai_directory": typeof openai_tools_openai_directory;
  "openai_tools/openai_projects": typeof openai_tools_openai_projects;
  "openai_tools/openai_relationships": typeof openai_tools_openai_relationships;
  organizations: typeof organizations;
  people: typeof people;
  projectUpdates: typeof projectUpdates;
  projects: typeof projects;
  prompts: typeof prompts;
  "relationships/fileRelationships": typeof relationships_fileRelationships;
  "relationships/relationshipMutations": typeof relationships_relationshipMutations;
  "relationships/relationshipQueries": typeof relationships_relationshipQueries;
  "relationships/teams": typeof relationships_teams;
  relationships: typeof relationships;
  "seeds/seedPrompts": typeof seeds_seedPrompts;
  "seeds/seedRelationshipTypes": typeof seeds_seedRelationshipTypes;
  "seeds/seed_badges": typeof seeds_seed_badges;
  "seeds/seed_integrations": typeof seeds_seed_integrations;
  "seeds/seed_people": typeof seeds_seed_people;
  "seeds/seed_tagTypes_step1": typeof seeds_seed_tagTypes_step1;
  "seeds/seed_tags_step2": typeof seeds_seed_tags_step2;
  semanticSearch: typeof semanticSearch;
  tags: typeof tags;
  tasks: typeof tasks;
  teams: typeof teams;
  "trieve/deleteDocument": typeof trieve_deleteDocument;
  "trieve/trieve": typeof trieve_trieve;
  universalSearch: typeof universalSearch;
  updateDocument: typeof updateDocument;
  users: typeof users;
  "utils/fieldUtils": typeof utils_fieldUtils;
  "utils/fileTypes": typeof utils_fileTypes;
  "utils/fileUtils": typeof utils_fileUtils;
  "utils/mentions": typeof utils_mentions;
  "utils/schedulerUtils": typeof utils_schedulerUtils;
  "utils/shortDescriptionUtils": typeof utils_shortDescriptionUtils;
  "utils/trieveUtils": typeof utils_trieveUtils;
  welcome: typeof welcome;
}>;
declare const fullApiWithMounts: typeof fullApi;

export declare const api: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "internal">
>;

export declare const components: {
  aggregate: {
    btree: {
      aggregateBetween: FunctionReference<
        "query",
        "internal",
        { k1?: any; k2?: any; namespace?: any },
        { count: number; sum: number }
      >;
      atNegativeOffset: FunctionReference<
        "query",
        "internal",
        { k1?: any; k2?: any; namespace?: any; offset: number },
        { k: any; s: number; v: any }
      >;
      atOffset: FunctionReference<
        "query",
        "internal",
        { k1?: any; k2?: any; namespace?: any; offset: number },
        { k: any; s: number; v: any }
      >;
      get: FunctionReference<
        "query",
        "internal",
        { key: any; namespace?: any },
        null | { k: any; s: number; v: any }
      >;
      offset: FunctionReference<
        "query",
        "internal",
        { k1?: any; key: any; namespace?: any },
        number
      >;
      offsetUntil: FunctionReference<
        "query",
        "internal",
        { k2?: any; key: any; namespace?: any },
        number
      >;
      paginate: FunctionReference<
        "query",
        "internal",
        {
          cursor?: string;
          k1?: any;
          k2?: any;
          limit: number;
          namespace?: any;
          order: "asc" | "desc";
        },
        {
          cursor: string;
          isDone: boolean;
          page: Array<{ k: any; s: number; v: any }>;
        }
      >;
      paginateNamespaces: FunctionReference<
        "query",
        "internal",
        { cursor?: string; limit: number },
        { cursor: string; isDone: boolean; page: Array<any> }
      >;
      validate: FunctionReference<
        "query",
        "internal",
        { namespace?: any },
        any
      >;
    };
    inspect: {
      display: FunctionReference<"query", "internal", { namespace?: any }, any>;
      dump: FunctionReference<"query", "internal", { namespace?: any }, string>;
      inspectNode: FunctionReference<
        "query",
        "internal",
        { namespace?: any; node?: string },
        null
      >;
    };
    public: {
      clear: FunctionReference<
        "mutation",
        "internal",
        { maxNodeSize?: number; namespace?: any; rootLazy?: boolean },
        null
      >;
      deleteIfExists: FunctionReference<
        "mutation",
        "internal",
        { key: any; namespace?: any },
        any
      >;
      delete_: FunctionReference<
        "mutation",
        "internal",
        { key: any; namespace?: any },
        null
      >;
      init: FunctionReference<
        "mutation",
        "internal",
        { maxNodeSize?: number; namespace?: any; rootLazy?: boolean },
        null
      >;
      insert: FunctionReference<
        "mutation",
        "internal",
        { key: any; namespace?: any; summand?: number; value: any },
        null
      >;
      makeRootLazy: FunctionReference<
        "mutation",
        "internal",
        { namespace?: any },
        null
      >;
      replace: FunctionReference<
        "mutation",
        "internal",
        {
          currentKey: any;
          namespace?: any;
          newKey: any;
          newNamespace?: any;
          summand?: number;
          value: any;
        },
        null
      >;
      replaceOrInsert: FunctionReference<
        "mutation",
        "internal",
        {
          currentKey: any;
          namespace?: any;
          newKey: any;
          newNamespace?: any;
          summand?: number;
          value: any;
        },
        any
      >;
    };
  };
  migrations: {
    lib: {
      cancel: FunctionReference<
        "mutation",
        "internal",
        { name: string },
        {
          batchSize?: number;
          cursor?: string | null;
          error?: string;
          isDone: boolean;
          latestEnd?: number;
          latestStart: number;
          name: string;
          next?: Array<string>;
          processed: number;
          state: "inProgress" | "success" | "failed" | "canceled" | "unknown";
        }
      >;
      cancelAll: FunctionReference<
        "mutation",
        "internal",
        { sinceTs?: number },
        Array<{
          batchSize?: number;
          cursor?: string | null;
          error?: string;
          isDone: boolean;
          latestEnd?: number;
          latestStart: number;
          name: string;
          next?: Array<string>;
          processed: number;
          state: "inProgress" | "success" | "failed" | "canceled" | "unknown";
        }>
      >;
      clearAll: FunctionReference<
        "mutation",
        "internal",
        { before?: number },
        null
      >;
      getStatus: FunctionReference<
        "query",
        "internal",
        { limit?: number; names?: Array<string> },
        Array<{
          batchSize?: number;
          cursor?: string | null;
          error?: string;
          isDone: boolean;
          latestEnd?: number;
          latestStart: number;
          name: string;
          next?: Array<string>;
          processed: number;
          state: "inProgress" | "success" | "failed" | "canceled" | "unknown";
        }>
      >;
      migrate: FunctionReference<
        "mutation",
        "internal",
        {
          batchSize?: number;
          cursor?: string | null;
          dryRun: boolean;
          fnHandle: string;
          name: string;
          next?: Array<{ fnHandle: string; name: string }>;
        },
        {
          batchSize?: number;
          cursor?: string | null;
          error?: string;
          isDone: boolean;
          latestEnd?: number;
          latestStart: number;
          name: string;
          next?: Array<string>;
          processed: number;
          state: "inProgress" | "success" | "failed" | "canceled" | "unknown";
        }
      >;
    };
  };
};

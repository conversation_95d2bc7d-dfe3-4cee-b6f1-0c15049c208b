"use node";

import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { zCustomAction } from "convex-helpers/server/zod";
import { NoOp } from "convex-helpers/server/customFunctions";
import { internalAction, action } from "../_generated/server";
import { internal, api } from "../_generated/api";
import { BoxClient, BoxDeveloperTokenAuth } from 'box-typescript-sdk-gen';

// Import Box schema for file type validation and action schemas
import { 
  BOX_SIGN_SUPPORTED_EXTENSIONS,
  SendForSignatureArgsSchema,
  GetOrCreateSignRequestsFolderArgsSchema,
  CreateSignRequestArgsSchema,
  GetSignRequestStatusArgsSchema,
  FetchSignRequestArgsSchema,
  SignRequestResponseSchema
} from './boxSchema';

/**
 * Wrapper around Convex's action function that uses Zod for validation.
 */
const zAction = zCustomAction(action, NoOp);

/**
 * Wrapper around Convex's internalAction function that uses Zod for validation.
 */
const zInternalAction = zCustomAction(internalAction, NoOp);



// Helper function to validate Box Sign compatible file types
function isBoxSignCompatibleFileType(fileExtension: string | undefined, fileName: string): boolean {
    if (!fileExtension && !fileName) return false;
    
    // Extract extension from filename if not provided
    const ext = fileExtension || (fileName.includes('.') ? fileName.split('.').pop()?.toLowerCase() : '');
    if (!ext) return false;
    
    return BOX_SIGN_SUPPORTED_EXTENSIONS.includes(ext.toLowerCase() as any);
}

/**
 * sendForSignature action
 *
 * @description
 * Sends a document for signature using Box Sign. Validates file compatibility,
 * creates a sign request, and returns the preparation URL.
 *
 * @example
 * const prepareUrl = await convex.runAction(api.box.boxSignatureActions.sendForSignature, {
 *   documentId: "j57abc123...",
 *   signerEmail: "<EMAIL>"
 * });
 *
 * @throws {ZodError} If documentId or signerEmail are invalid.
 */
export const sendForSignature = zAction({
    args: {
        documentId: zid("documents"),
        signerEmail: z.string().email(),
    },
    returns: z.string().nullable(),
    handler: async (ctx, args): Promise<string | null> => {
        const { documentId, signerEmail } = SendForSignatureArgsSchema.parse(args);

        const document = await ctx.runQuery(api.files.files.getDocumentById, { documentId });
        if (!document) {
            throw new Error("Document not found");
        }

        const file = await ctx.runQuery(internal.files.files.getFile, { fileId: document.fileId });
        if (!file || !file.box_file_id) {
            throw new Error("Box file ID not found for this document");
        }

        // Validate file type compatibility with Box Sign
        const fileName = file.fileName || file.title || '';
        if (!isBoxSignCompatibleFileType(file.fileExtension, fileName)) {
            throw new Error(`File type not supported by Box Sign. File: "${fileName}" with extension: "${file.fileExtension}". Supported types include: PDF, DOC, DOCX, TXT, PNG, JPG, and others.`);
        }

        // Log file details for debugging
        console.log(`Attempting to send file for signature:`, {
            fileId: file._id,
            fileName: file.fileName,
            fileExtension: file.fileExtension,
            boxFileId: file.box_file_id,
            signerEmail,
            isCompatible: true
        });

        // Get or create a proper destination folder (not root folder which is forbidden)
        // Using known working folder ID for now
        const destinationFolderId = '325866844558'; // Known working folder ID

        const { id, prepare_url } = await ctx.runAction(internal.box.boxSignatureActions.createSignRequest, {
            boxFileId: file.box_file_id,
            destinationFolderId,
            signerEmail,
        });

        await ctx.runMutation(internal.box.signatureMutations.internalSaveSignRequestId, {
            documentId,
            signRequestId: id,
        });

        return prepare_url;
    },
});

/**
 * getOrCreateSignRequestsFolder internal action
 *
 * @description
 * Gets or creates a "Sign Requests" folder in Box for organizing signed documents.
 *
 * @throws {ZodError} If sourceFileId is invalid.
 */
export const getOrCreateSignRequestsFolder = zInternalAction({
    args: {
        sourceFileId: z.string(),
    },
    returns: z.string(),
    handler: async (ctx, args): Promise<string> => {
        const { sourceFileId } = GetOrCreateSignRequestsFolderArgsSchema.parse(args);

        const integration = await ctx.runQuery(api.files.files.getIntegrations, {});

        if (!integration || !integration.config?.developerToken) {
            throw new Error("Box integration not configured or developer token is missing.");
        }

        const auth = new BoxDeveloperTokenAuth({ token: integration.config.developerToken });
        const client = new BoxClient({ auth });

        try {
            // First, try to get the parent folder of the source file
            const sourceFileInfo = await client.files.getFileById(sourceFileId);
            if (sourceFileInfo.parent && sourceFileInfo.parent.id && sourceFileInfo.parent.id !== '0') {
                console.log(`Using source file's parent folder: ${sourceFileInfo.parent.id}`);
                return sourceFileInfo.parent.id;
            }
        } catch (error) {
            console.warn(`Could not get source file parent folder, will create sign requests folder:`, error);
        }

        try {
            // Search for existing "Sign Requests" folder in root
            const searchResults = await client.search.searchForContent({
                query: 'Sign Requests',
                type: 'folder' as const,
                ancestorFolderIds: ['0']
            });

            // Check if we found a "Sign Requests" folder
            for (const item of searchResults.entries || []) {
                if (item.type === 'folder' && 'name' in item && item.name === 'Sign Requests') {
                    console.log(`Found existing Sign Requests folder: ${item.id}`);
                    return item.id!;
                }
            }

            // Create "Sign Requests" folder if it doesn't exist
            const newFolder = await client.folders.createFolder({
                name: 'Sign Requests',
                parent: { id: '0' }
            });

            console.log(`Created new Sign Requests folder: ${newFolder.id}`);
            return newFolder.id;

        } catch (error: any) {
            console.error(`Failed to create/find Sign Requests folder:`, error);
            // As a fallback, return the hardcoded folder ID
            return '325866844558';
        }
    },
});

/**
 * createSignRequest internal action
 *
 * @description
 * Creates a Box Sign request with specified parameters.
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const createSignRequest = zInternalAction({
    args: {
        boxFileId: z.string(),
        destinationFolderId: z.string(),
        signerEmail: z.string().email(),
    },
    returns: SignRequestResponseSchema,
    handler: async (ctx, args): Promise<z.infer<typeof SignRequestResponseSchema>> => {
        const { boxFileId, destinationFolderId, signerEmail } = CreateSignRequestArgsSchema.parse(args);

        const integration = await ctx.runQuery(api.files.files.getIntegrations, {});

        if (!integration || !integration.config?.developerToken) {
            throw new Error("Box integration not configured or developer token is missing.");
        }

        const auth = new BoxDeveloperTokenAuth({ token: integration.config.developerToken });
        const client = new BoxClient({ auth });

        // Log the request parameters for debugging
        const requestParams = {
            sourceFiles: [{ id: boxFileId, type: 'file' as const }],
            parentFolder: { id: destinationFolderId, type: 'folder' as const },
            signers: [{ email: signerEmail, role: 'signer' as const }],
            isDocumentPreparationNeeded: true,
        };
        
        console.log(`Creating Box Sign request with parameters:`, JSON.stringify(requestParams, null, 2));

        try {
            const signRequest = await client.signRequests.createSignRequest(requestParams);

            console.log(`Successfully created sign request:`, {
                id: signRequest.id,
                status: signRequest.status,
                prepareUrl: signRequest.prepareUrl ? 'URL_PROVIDED' : 'NO_URL'
            });

            const result = {
                id: signRequest.id || '',
                prepare_url: signRequest.prepareUrl || null,
            };

            return SignRequestResponseSchema.parse(result);
        } catch (error: any) {
            // Enhanced error logging to capture Box API error details
            console.error(`Box Sign API Error Details:`, {
                message: error.message,
                status: error.status,
                code: error.code,
                requestId: error.requestId,
                contextInfo: error.contextInfo,
                helpUrl: error.helpUrl,
                requestParams: requestParams,
                errorName: error.name,
                errorConstructor: error.constructor.name,
                allErrorProperties: Object.getOwnPropertyNames(error),
                fullError: JSON.stringify(error, Object.getOwnPropertyNames(error), 2)
            });

            // If there's context info with specific field errors, log those too
            if (error.contextInfo && error.contextInfo.errors) {
                console.error(`Specific field errors:`, error.contextInfo.errors);
            }

            // If there are response details, log those
            if (error.response) {
                console.error(`HTTP Response details:`, {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                });
            }

            // Re-throw with more descriptive error message
            throw new Error(`Box Sign API Error: ${error.status || error.response?.status} "${error.message}". Request ID: ${error.requestId || 'unknown'}. Check logs for detailed error information.`);
        }
    },
});

/**
 * getSignRequestStatus action
 *
 * @description
 * Gets the status of a Box Sign request by ID.
 *
 * @throws {ZodError} If signRequestId is invalid.
 */
export const getSignRequestStatus = zAction({
    args: {
        signRequestId: z.string(),
    },
    returns: z.any(),
    handler: async (ctx, args): Promise<any> => {
        const { signRequestId } = GetSignRequestStatusArgsSchema.parse(args);
        return await ctx.runAction(internal.box.boxSignatureActions.fetchSignRequest, { signRequestId });
    },
});

/**
 * fetchSignRequest internal action
 *
 * @description
 * Fetches a Box Sign request by ID from the Box API.
 *
 * @throws {ZodError} If signRequestId is invalid.
 */
export const fetchSignRequest = zInternalAction({
    args: {
        signRequestId: z.string(),
    },
    returns: z.any(),
    handler: async (ctx, args): Promise<any> => {
        const { signRequestId } = FetchSignRequestArgsSchema.parse(args);

        const integration = await ctx.runQuery(api.files.files.getIntegrations, {});

        if (!integration || !integration.config?.developerToken) {
            throw new Error("Box integration not configured or developer token is missing.");
        }

        const auth = new BoxDeveloperTokenAuth({ token: integration.config.developerToken });
        const client = new BoxClient({ auth });

        const signRequest = await client.signRequests.getSignRequestById(signRequestId);
        return signRequest;
    },
});

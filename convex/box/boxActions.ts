"use node";

import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { internal } from "../_generated/api";
import { zCustomAction } from "convex-helpers/server/zod";
import { NoOp } from "convex-helpers/server/customFunctions";
import { action } from "../_generated/server";
import { ConvertAndSaveMarkdownArgsSchema } from "./boxSchema";

/**
 * A wrapper around Convex's action function that uses Zod for validation.
 * Provides better type safety and more flexible validation.
 */
const zAction = zCustomAction(action, NoOp);



/**
 * convertAndSaveMarkdown action
 *
 * @description
 * Converts a document from storage and saves it as markdown using the LLM-friendly converter.
 * This action processes uploaded files and makes them accessible in a markdown format.
 *
 * @example
 * await ctx.runAction(internal.box.boxActions.convertAndSaveMarkdown, {
 *   documentId: "j57abc123...",
 *   storageId: "kg4def456..."
 * });
 *
 * @throws {ZodError} If documentId or storageId are invalid.
 */
export const convertAndSaveMarkdown = zAction({
  args: {
    documentId: zid("documents"),
    storageId: zid("_storage"),
  },
  returns: z.null(),
  handler: async (ctx, args) => {
    // Validate input with Zod
    const { documentId, storageId } = ConvertAndSaveMarkdownArgsSchema.parse(args);

    // Call the LLM-friendly converter action
    await ctx.runAction(internal.ai.llmFriendlyConverter.llmFriendlyConverter, {
      documentId,
      source: {
        type: "storage",
        storageId,
      },
    });

    return null;
  },
});

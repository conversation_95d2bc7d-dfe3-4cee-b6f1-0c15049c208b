import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

/**
 * Base schema for integration fields
 * Contains all fields that can be set when creating or updating an integration
 */
export const IntegrationBaseSchema = z.object({
  // Changed from integration_type to immutable_slug
  immutable_slug: z.string(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'NEEDS_SETUP', 'ERROR']),
  // Made display_name optional to match schema
  display_name: z.string().optional(),
  // Added description field
  description: z.string().optional(),
  // Updated to match the new schema with expected_config and user_config
  // Changed from z.record(z.string(), z.string()) to z.record(z.string(), z.any())
  // to support nested objects and complex configuration structures
  expected_config: z.record(z.string(), z.any()).optional(),
  user_config: z.record(z.string(), z.any()).optional(),
  // Made updated_at optional to match schema
  updated_at: z.number().optional()
});

/**
 * Complete integration schema including system fields
 * This represents an integration document as stored in the database
 */
export const IntegrationSchema = IntegrationBaseSchema.extend({
  _id: zid('integrations'),
  _creationTime: z.number()
});

/**
 * Schema for creating a new integration
 */
export const CreateIntegrationSchema = z.object({
  immutable_slug: z.string(),
  status: z.enum(["NEEDS_SETUP", "ACTIVE", "INACTIVE", "ERROR"]),
  display_name: z.string(),
  updated_at: z.number(),
  description: z.string().optional(),
  expected_config: z.record(z.any()).optional(),
  user_config: z.record(z.any()).optional(),
});

/**
 * Schema for updating an existing integration
 * All fields are optional since we only update what's provided
 */
export const UpdateIntegrationSchema = IntegrationBaseSchema.partial();

/**
 * Schema for integration input with ID
 * Used for operations that require an integration ID
 */
export const IntegrationWithIdSchema = z.object({
  integrationId: zid('integrations')
});

/**
 * Schema for filtering integrations
 * Used for search and list operations
 */
export const IntegrationFilterSchema = z.object({
  // Changed from integration_type to immutable_slug
  immutable_slug: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'NEEDS_SETUP', 'ERROR']).optional()
});

/**
 * Type definitions derived from Zod schemas
 */
export type Integration = z.infer<typeof IntegrationSchema>;
export type CreateIntegrationInput = z.infer<typeof CreateIntegrationSchema>;
export type UpdateIntegrationInput = z.infer<typeof UpdateIntegrationSchema>;
export type IntegrationWithId = z.infer<typeof IntegrationWithIdSchema>;
export type IntegrationFilter = z.infer<typeof IntegrationFilterSchema>;

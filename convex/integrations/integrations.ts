import { mutation, action, query } from "../_generated/server";
import { api } from "../_generated/api";
import { v } from "convex/values";
import { Integration } from "./integrationsSchema";

export const createIntegration = mutation({
  args: {
    integration: v.object({
      immutable_slug: v.string(),
      status: v.union(
        v.literal("NEEDS_SETUP"),
        v.literal("ACTIVE"),
        v.literal("INACTIVE"),
        v.literal("ERROR")
      ),
      display_name: v.string(),
      updated_at: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("integrations", args.integration);
  },
});

export const updateIntegration = mutation({
  args: {
    integrationId: v.id("integrations"),
    data: v.object({
      display_name: v.optional(v.string()),
      status: v.optional(v.union(
        v.literal("NEEDS_SETUP"),
        v.literal("ACTIVE"),
        v.literal("INACTIVE"),
        v.literal("ERROR")
      )),
      user_config: v.optional(v.any()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.integrationId, args.data);
  },
});

export const deleteIntegration = mutation({
    args: {
        integrationId: v.id('integrations')
    },
    handler: async (ctx, args) => {
        await ctx.db.delete(args.integrationId)
    }
})


export const get = query({
  args: {},
  returns: v.union(v.null(), v.object({
    _id: v.id("integrations"),
    _creationTime: v.number(),
    immutable_slug: v.string(),
    status: v.union(
      v.literal('ACTIVE'),
      v.literal('INACTIVE'),
      v.literal('NEEDS_SETUP'),
      v.literal('ERROR')
    ),
    display_name: v.optional(v.string()),
    description: v.optional(v.string()),
    expected_config: v.optional(v.record(v.string(), v.any())),
    user_config: v.optional(v.record(v.string(), v.any())),
    updated_at: v.optional(v.number()),
    last_sync_timestamp: v.optional(v.number())
  })),
  handler: async (ctx) => {
    const integrations = await ctx.db.query("integrations").collect();
    return integrations.find(integration => 
      integration.immutable_slug === "box"
    );
  },
});

export const listIntegrations = query({
  handler: async (ctx) => {
    const integrations = await ctx.db.query("integrations").collect();
    return integrations as unknown as Integration[];
  },
});

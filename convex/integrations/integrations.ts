import { zid } from "convex-helpers/server/zod";
import { zMutation, zQuery } from "../functions";
import {
  CreateIntegrationSchema,
  UpdateIntegrationSchema
} from "./integrationsSchema";

export const createIntegration = zMutation({
  args: {
    integration: CreateIntegrationSchema,
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("integrations", args.integration);
  },
});

export const updateIntegration = zMutation({
  args: {
    integrationId: zid("integrations"),
    data: UpdateIntegrationSchema,
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.integrationId, args.data);
  },
});

export const deleteIntegration = zMutation({
    args: {
        integrationId: zid('integrations')
    },
    handler: async (ctx, args) => {
        await ctx.db.delete(args.integrationId)
    }
})


export const get = zQuery({
  args: {},
  handler: async (ctx) => {
    const integrations = await ctx.db.query("integrations").collect();
    return integrations.find(integration =>
      integration.immutable_slug === "box"
    ) || null;
  },
});

export const listIntegrations = zQuery({
  args: {},
  handler: async (ctx) => {
    const integrations = await ctx.db.query("integrations").collect();
    return integrations;
  },
});
